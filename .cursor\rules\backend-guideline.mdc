---
description: 
globs: 
alwaysApply: true
---
# 后端开发规范与架构说明

- 本项目后端基于 [BladeX 框架](mdc:https:/www.kancloud.cn/smallchill/blade/3197976) 开发，采用 Spring Boot 架构，支持微服务、权限管理、工作流等功能。
- 主要入口文件为 [Application.java](mdc:backend/src/main/java/org/springblade/Application.java)。
- 主要目录结构：
  - `common/`：通用工具、配置、常量、枚举等
  - `flow/`：流程相关业务、核心、引擎等
  - `job/`：定时任务相关
  - `modules/`：各业务模块（如 auth、desk、develop、resource、system 等）
- 配置文件位于 `resources/` 目录下。
- 数据库脚本、部署脚本等位于 `doc/` 目录下。
- 代码开发严格遵循 [阿里巴巴 Java 开发手册](mdc:https:/github.com/alibaba/p3c) 规范。
- 详细开发文档请参考 [BladeX 官方文档](mdc:https:/www.kancloud.cn/smallchill/blade/3197976)。

