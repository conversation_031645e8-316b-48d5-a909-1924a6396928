---
description:
globs:
alwaysApply: false
---
# cursor/rules.yaml
# 前端页面风格与组件开发统一规范

page-style:
  layout:
    - 采用左右分栏布局，左侧为树形菜单，右侧为内容区
    - 外层统一使用 <basic-container> 组件包裹
    - 内容区主要用 <avue-crud> 实现表格、表单、分页、搜索
    - 操作按钮区放在表格上方，按钮使用 <el-button>，权限用 v-if 控制
  theme:
    - 全局背景色 #f0f2f5，内容区白色
    - 主题色为蓝色渐变（#006cff ~ #399efd），用于菜单、按钮、logo等
    - 组件圆角 3~4px，表格、按钮、标签等统一风格
    - 字体大小、间距、padding、margin 统一
    - 响应式布局，适配不同屏幕
  component:
    - 列表、表单、弹窗等用 avue 体系（<avue-crud>、<avue-form>、<avue-tree>）
    - 弹窗统一用 <el-dialog>，宽度、标题、按钮风格统一
    - 标签用 <el-tag>，状态/类型高亮
    - 权限控制通过 permission 对象和 v-if 实现
    - 事件命名规范：row-del、row-update、row-save、search-change 等
  code-style:
    - 变量、方法、事件命名采用驼峰命名
    - 组件分区明确，模板、脚本、样式分离
    - 充分利用 slot、template 进行自定义渲染
  scss:
    - 全局样式变量、主题色、圆角、阴影等在 styles/common.scss、styles/theme/beautiful.scss 统一定义
    - 组件样式可按需覆盖，但需遵循主题色、圆角、字体等全局规范

# 使用说明
# 1. 新建页面时，必须以 <basic-container> 为外层，内容区用 <avue-crud> 或 <avue-form>。
# 2. 按钮、弹窗、标签等组件风格需与主题色、圆角、字体等全局样式保持一致。
# 3. 权限、事件、数据流转等需遵循 user.vue 的实现方式。
# 4. 如需自定义样式，优先在 scss 变量中扩展，避免直接写死颜色、尺寸。