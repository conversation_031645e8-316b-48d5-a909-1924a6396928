---
description: 
globs: 
alwaysApply: true
---
# 前端开发规范与架构说明

- 本项目前端基于 [Saber 前端方案](mdc:https:/www.kancloud.cn/smallchill/saber/1302055) 开发，采用 Vue.js 作为主框架，配合 Element UI、Avue 等组件库。
- 主要入口文件为 [App.vue](mdc:frontend/src/App.vue)。
- 主要目录结构：
  - `api/`：所有后端接口请求封装
  - `components/`：通用组件
  - `views/`：各业务页面
  - `router/`：路由配置
  - `store/`：Vuex 状态管理
  - `styles/`：全局样式与主题
  - `utils/`：工具函数
  - `public/`：静态资源
- 推荐遵循官方 Vue 3 及 Saber 文档的最佳实践进行开发。
- 详细开发文档请参考 [Saber 官方文档](mdc:https:/www.kancloud.cn/smallchill/saber/1302055)。

