---
description: 
globs: 
alwaysApply: true
---
# 项目结构说明

本项目分为后端（backend）和前端（frontend）两部分：

- 后端目录为 [backend/](mdc:backend)，主要包含 Java 代码、配置文件、数据库脚本、Docker 部署脚本等，主入口为 [Application.java](mdc:backend/src/main/java/org/springblade/Application.java)。
- 前端目录为 [frontend/](mdc:frontend)，主要包含 Vue.js 源码、静态资源、配置文件等，主入口为 [App.vue](mdc:frontend/src/App.vue)。

详细功能、使用方法和参数说明请参考 [backend/README.md](mdc:backend/README.md)。

