# 知慧会务系统（全栈版）

## 一、项目简介

知慧会务系统是一个帮助你轻松管理会议的全套软件。它包含：
- 会议官网（H5端）：参会人员报名、查看议程、个人中心等
- 管理后台：管理员管理会议内容、报名信息、数据统计等
- 后端API服务：负责数据存储、用户认证、权限管理等

本系统适合学校、企业、组织举办会议时使用，支持快速搭建和二次开发。

---

## 二、项目架构

```
会务系统/
├── backend/           # 后端API服务（Node.js + Express，默认用PostgreSQL数据库）
├── admin-frontend/    # 管理后台（Vue3 + Vite + Element Plus）
├── demo/              # 会议官网/参会端（H5静态页面）
└── README.md          # 项目说明书（本文件）
```

- **H5端**（demo/）：参会人员报名、查看议程、个人中心等
- **管理后台**（admin-frontend/）：管理员管理会议内容、报名信息、数据统计等
- **后端API**（backend/）：提供数据接口，负责数据存储、用户认证等

---

## 三、主要功能

### H5端（demo/）
- 会议议程：查看会议详细日程安排
- 云直播：在线观看会议直播
- 分会场信息：了解各分会场的安排和信息
- 会议资料：下载和查阅会议相关文件
- 在线相册：浏览会议照片
- 参会指南：获取会议交通、住宿等参会须知
- 会务助手：AI智能问答、会务咨询
- 个人中心：管理个人信息、查看参会记录

### 管理后台（admin-frontend/）
- 内容管理：议程、报名表、图片等
- 用户管理：参会人员、管理员
- 数据统计：报名人数、签到情况等
- 权限管理：不同角色分工

### 后端API（backend/）
- 提供RESTful API接口
- 数据持久化存储（默认PostgreSQL）
- 用户认证与权限管理

---

## 四、快速开始（适合新手）

1. **克隆项目**
2. **安装依赖**
   - 后端：进入`backend/`，运行`npm install`
   - 管理后台：进入`admin-frontend/`，运行`npm install`
3. **安装PostgreSQL数据库**（推荐）
   - [官网下载](https://www.postgresql.org/download/)并安装
   - 创建数据库和用户，例如：
     ```sql
     CREATE DATABASE conference;
     CREATE USER conf_user WITH PASSWORD '你的密码';
     GRANT ALL PRIVILEGES ON DATABASE conference TO conf_user;
     ```
4. **配置数据库连接**
   - 修改`backend/config.js`或`.env`，填写数据库信息：
     ```
     DB_DIALECT=postgres
     DB_HOST=localhost
     DB_PORT=5432
     DB_NAME=conference
     DB_USER=conf_user
     DB_PASS=你的密码
     ```
5. **安装后端数据库依赖**
   - 进入`backend/`目录，运行：
     ```bash
     npm install pg pg-hstore sequelize
     ```
6. **启动后端服务**
   ```bash
   cd backend
   npm run dev
   # 或 node src/index.js
   ```
7. **开发/打包前端**
   - 管理后台：`npm run dev`（开发），`npm run build`（打包后将dist内容复制到backend/public/admin）
   - H5端：可直接用静态文件，也可打包后放到backend/public/h5
8. **访问系统**
   - 管理后台：http://localhost:端口/admin
   - H5端：http://localhost:端口/h5

---

## 五、详细开发流程

1. 克隆代码到本地
2. 安装依赖（见上）
3. 配置数据库（见上）
4. 启动后端服务
5. 启动前端（管理后台、H5端）
6. 按需修改页面内容、样式或功能
7. 如需上线，建议用Nginx反向代理，开启HTTPS

---

## 六、数据库说明

- **默认数据库：PostgreSQL**（推荐，安全、稳定、免费）
- 也支持SQLite（适合轻量级、本地测试）
- 切换方法：
  1. 安装SQLite
  2. 修改`backend/config.js`或`.env`，将`DB_DIALECT`改为`sqlite`，并配置数据库文件路径
  3. 安装依赖：`npm install sqlite3`
  4. 业务代码无需更改

---

## 七、常见问题

- **数据库连接失败**：请检查数据库服务是否启动、账号密码是否正确、端口是否开放
- **依赖未安装**：请确保已运行`npm install pg pg-hstore sequelize`
- **端口冲突**：如端口被占用，修改`backend`的端口配置
- **前端页面空白**：确认打包后的静态文件已放到`backend/public`目录
- **安全建议**：生产环境请务必设置强密码、开启防火墙、使用HTTPS

---

## 八、后续扩展建议

- 集成更多后端API，实现数据实时交互
- 添加用户认证系统，支持微信/短信登录
- 实现推送通知、消息提醒
- 优化性能和SEO，提升访问速度
- 支持多语言切换

---

## 九、联系方式

如有问题或建议，请联系开发团队，或在项目Issue区留言。

---

© 2025 广州阅数科技有限公司. 保留所有权利。
