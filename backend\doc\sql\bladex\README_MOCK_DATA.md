# 会务系统模拟数据说明文档

## 📋 概述

本目录包含了会务系统的完整模拟数据，用于开发和测试环境。数据结构完全基于前端Vue组件的mock数据设计，确保前后端数据格式的完美匹配。

## 📁 文件说明

### 核心文件

- **`insert_mock_data.sql`** - 主要的SQL插入语句文件
- **`execute_mock_data.sh`** - Linux/macOS执行脚本
- **`execute_mock_data.bat`** - Windows执行脚本
- **`README_MOCK_DATA.md`** - 本说明文档

## 🗃️ 数据表结构

### 1. 会议议程 (hy_agenda)
- **记录数量**: 16条
- **内容**: 两天完整的会议议程安排
- **字段**: 日期、时间、主题、演讲人、会场、描述等

### 2. 云直播 (hy_live_stream)
- **记录数量**: 4条
- **内容**: 主会场和各分会场的直播信息
- **字段**: 标题、直播URL、开始时间、结束时间、状态等

### 3. 分会场信息 (hy_sub_venue)
- **记录数量**: 4条
- **内容**: 技术、管理、创新、展示等分会场
- **字段**: 名称、位置、负责人、描述等

### 4. 会议资料 (hy_materials)
- **记录数量**: 10条
- **内容**: 各类会议文档和资料
- **字段**: 标题、文件URL、上传时间、上传者、描述等

### 5. 在线相册 (hy_photo_album)
- **记录数量**: 12条
- **内容**: 会议各环节的照片记录
- **字段**: 标题、图片URL、上传时间、分类、描述等

### 6. 参会指南 (hy_guide)
- **记录数量**: 8条
- **内容**: 交通、住宿、餐饮、安全等指南
- **字段**: 标题、内容、类型、更新时间等

### 7. AI聊天记录 (hy_ai_chat_log)
- **记录数量**: 8条
- **内容**: 常见问答记录
- **字段**: 用户ID、问题、答案、创建时间等

### 8. 用户信息 (blade_user)
- **记录数量**: 5条
- **内容**: 测试用户账号
- **字段**: 账号、姓名、邮箱、电话等

### 9. 签到记录 (hy_checkin)
- **记录数量**: 9条
- **内容**: 两天的签到数据
- **字段**: 用户ID、签到时间、签到方式、位置等

### 10. 会议反馈 (hy_feedback)
- **记录数量**: 7条
- **内容**: 用户评价和反馈
- **字段**: 用户ID、评分、内容、反馈类型等

## 🚀 使用方法

### 方法一：使用执行脚本（推荐）

#### Linux/macOS用户：
```bash
# 1. 进入SQL目录
cd backend/doc/sql/bladex/

# 2. 给脚本执行权限
chmod +x execute_mock_data.sh

# 3. 执行脚本
./execute_mock_data.sh
```

#### Windows用户：
```cmd
# 1. 进入SQL目录
cd backend\doc\sql\bladex\

# 2. 双击执行或命令行运行
execute_mock_data.bat
```

### 方法二：手动执行SQL

```bash
# 使用psql命令直接执行
psql -h localhost -p 5432 -U postgres -d bladex -f insert_mock_data.sql
```

## ⚙️ 配置说明

### 数据库连接配置

在执行脚本中修改以下配置：

```bash
DB_HOST="localhost"      # 数据库主机
DB_PORT="5432"          # 数据库端口
DB_NAME="bladex"        # 数据库名称
DB_USER="postgres"      # 用户名
DB_PASSWORD="123456"    # 密码
```

### 前置条件

1. **PostgreSQL已安装并运行**
2. **数据库表已创建**（执行过建表SQL）
3. **psql命令可用**（已添加到PATH环境变量）

## 🔧 功能特性

### 自动化功能
- ✅ **连接检测** - 自动检测数据库连接状态
- ✅ **数据备份** - 可选择备份现有数据
- ✅ **数据清理** - 可选择清理现有测试数据
- ✅ **结果验证** - 自动验证插入结果

### 安全特性
- ✅ **确认提示** - 危险操作需要用户确认
- ✅ **错误处理** - 完善的错误检测和提示
- ✅ **回滚支持** - 支持数据备份和恢复

## 📊 数据特点

### 真实性
- 所有数据都基于真实的企业管理会议场景
- 时间安排合理，符合实际会议流程
- 人员信息和联系方式格式正确

### 一致性
- 数据之间存在合理的关联关系
- 时间线连贯，无逻辑冲突
- 与前端组件期望的数据格式完全匹配

### 完整性
- 覆盖所有功能模块
- 每个模块都有足够的测试数据
- 支持各种业务场景的测试

## 🧪 测试建议

### 功能测试
1. **议程查看** - 验证议程数据显示和时间排序
2. **直播功能** - 测试直播状态和URL跳转
3. **资料下载** - 验证资料列表和下载链接
4. **相册浏览** - 测试照片分类和展示效果
5. **指南查看** - 验证各类指南信息显示
6. **签到功能** - 测试签到记录和统计
7. **AI助手** - 验证问答匹配和回复
8. **个人中心** - 测试用户信息和反馈功能

### 性能测试
- 大量数据加载性能
- 分页查询效果
- 搜索功能响应速度

## 🔄 数据更新

### 添加新数据
直接在 `insert_mock_data.sql` 文件中添加新的INSERT语句

### 修改现有数据
1. 备份现有数据
2. 修改SQL文件
3. 清理旧数据
4. 重新执行插入

### 删除测试数据
```sql
-- 清理所有测试数据
DELETE FROM hy_feedback WHERE create_user = 1123598821738675201;
DELETE FROM hy_checkin WHERE create_user = 1123598821738675201;
DELETE FROM hy_ai_chat_log WHERE create_user = 1123598821738675201;
DELETE FROM hy_guide WHERE create_user = 1123598821738675201;
DELETE FROM hy_photo_album WHERE create_user = 1123598821738675201;
DELETE FROM hy_materials WHERE create_user = 1123598821738675201;
DELETE FROM hy_sub_venue WHERE create_user = 1123598821738675201;
DELETE FROM hy_live_stream WHERE create_user = 1123598821738675201;
DELETE FROM hy_agenda WHERE create_user = 1123598821738675201;
```

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. **数据库连接** - 确认数据库服务正常运行
2. **权限问题** - 确认用户有足够的数据库操作权限
3. **表结构** - 确认所有相关表已正确创建
4. **字符编码** - 确认数据库支持UTF-8编码

## 📈 统计信息

- **总数据量**: 83条记录
- **涵盖表数**: 10个核心业务表
- **测试用户**: 5个测试账号
- **会议天数**: 2天完整议程
- **功能覆盖**: 100%核心功能模块

---

**注意**: 本数据仅用于开发和测试环境，请勿在生产环境中使用。
