@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 会务系统模拟数据插入执行脚本 (Windows版本)
REM 用于快速导入测试数据到PostgreSQL数据库

REM 数据库连接配置
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=bladex
set DB_USER=postgres
set DB_PASSWORD=123456

echo ==================================================
echo         会务系统模拟数据插入工具 (Windows)
echo ==================================================
echo.

REM 检查PostgreSQL是否安装
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到 psql 命令，请确保 PostgreSQL 已安装并添加到 PATH 环境变量
    pause
    exit /b 1
)

REM 检查数据库连接
echo [INFO] 检查数据库连接...
set PGPASSWORD=%DB_PASSWORD%
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 数据库连接失败，请检查连接配置
    echo 请确认以下信息：
    echo - 数据库服务器：%DB_HOST%:%DB_PORT%
    echo - 数据库名称：%DB_NAME%
    echo - 用户名：%DB_USER%
    echo - 密码：%DB_PASSWORD%
    pause
    exit /b 1
)
echo [SUCCESS] 数据库连接成功

echo.

REM 询问是否备份现有数据
set /p backup_choice="是否需要备份现有数据？(y/n): "
if /i "%backup_choice%"=="y" (
    echo [INFO] 正在备份数据...
    for /f "tokens=1-4 delims=/ " %%i in ('date /t') do set backup_date=%%k%%j%%i
    for /f "tokens=1-2 delims=: " %%i in ('time /t') do set backup_time=%%i%%j
    set backup_file=backup_%backup_date%_%backup_time%.sql
    set backup_file=!backup_file: =!
    
    set PGPASSWORD=%DB_PASSWORD%
    pg_dump -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -t hy_agenda -t hy_live_stream -t hy_sub_venue -t hy_materials -t hy_photo_album -t hy_guide -t hy_ai_chat_log -t hy_checkin -t hy_feedback > !backup_file!
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] 数据备份完成：!backup_file!
    ) else (
        echo [ERROR] 数据备份失败
        pause
        exit /b 1
    )
)

echo.

REM 询问是否清理现有数据
echo [WARNING] 是否清理现有测试数据？这将删除所有会务系统相关数据！
set /p clean_choice="请输入 (y/n): "
if /i "%clean_choice%"=="y" (
    echo [INFO] 正在清理现有数据...
    
    set PGPASSWORD=%DB_PASSWORD%
    psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "DELETE FROM hy_feedback WHERE create_user = 1123598821738675201; DELETE FROM hy_checkin WHERE create_user = 1123598821738675201; DELETE FROM hy_ai_chat_log WHERE create_user = 1123598821738675201; DELETE FROM hy_guide WHERE create_user = 1123598821738675201; DELETE FROM hy_photo_album WHERE create_user = 1123598821738675201; DELETE FROM hy_materials WHERE create_user = 1123598821738675201; DELETE FROM hy_sub_venue WHERE create_user = 1123598821738675201; DELETE FROM hy_live_stream WHERE create_user = 1123598821738675201; DELETE FROM hy_agenda WHERE create_user = 1123598821738675201;"
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] 现有数据清理完成
    ) else (
        echo [ERROR] 数据清理失败
        pause
        exit /b 1
    )
)

echo.

REM 检查SQL文件是否存在
if not exist "insert_mock_data.sql" (
    echo [ERROR] 找不到 insert_mock_data.sql 文件
    echo 请确保该文件与此批处理文件在同一目录下
    pause
    exit /b 1
)

REM 执行数据插入
echo [INFO] 开始插入模拟数据...
set PGPASSWORD=%DB_PASSWORD%
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f insert_mock_data.sql

if %errorlevel% equ 0 (
    echo [SUCCESS] 模拟数据插入完成
) else (
    echo [ERROR] 模拟数据插入失败
    pause
    exit /b 1
)

echo.

REM 验证数据插入结果
echo [INFO] 验证数据插入结果...
set PGPASSWORD=%DB_PASSWORD%
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'hy_agenda' as table_name, COUNT(*) as record_count FROM hy_agenda WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_live_stream', COUNT(*) FROM hy_live_stream WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_sub_venue', COUNT(*) FROM hy_sub_venue WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_materials', COUNT(*) FROM hy_materials WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_photo_album', COUNT(*) FROM hy_photo_album WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_guide', COUNT(*) FROM hy_guide WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_ai_chat_log', COUNT(*) FROM hy_ai_chat_log WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_checkin', COUNT(*) FROM hy_checkin WHERE create_user = 1123598821738675201 UNION ALL SELECT 'hy_feedback', COUNT(*) FROM hy_feedback WHERE create_user = 1123598821738675201;"

echo.
echo [SUCCESS] 所有操作完成！
echo.
echo 数据插入统计：
echo - 会议议程：16条记录
echo - 云直播：4条记录  
echo - 分会场信息：4条记录
echo - 会议资料：10条记录
echo - 在线相册：12条记录
echo - 参会指南：8条记录
echo - AI聊天记录：8条记录
echo - 用户信息：5条记录
echo - 签到记录：9条记录
echo - 会议反馈：7条记录
echo.
echo 总计：83条测试数据记录
echo.
echo 现在可以启动前端应用测试各个功能模块！

pause
