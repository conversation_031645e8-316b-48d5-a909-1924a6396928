#!/bin/bash

# 会务系统模拟数据插入执行脚本
# 用于快速导入测试数据到PostgreSQL数据库

# 数据库连接配置
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="bladex"
DB_USER="postgres"
DB_PASSWORD="123456"

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查PostgreSQL连接
check_connection() {
    log_info "检查数据库连接..."
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败，请检查连接配置"
        return 1
    fi
}

# 备份现有数据（可选）
backup_data() {
    log_info "是否需要备份现有数据？(y/n)"
    read -r backup_choice
    
    if [ "$backup_choice" = "y" ] || [ "$backup_choice" = "Y" ]; then
        backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
        log_info "正在备份数据到 $backup_file..."
        
        PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
            -t hy_agenda -t hy_live_stream -t hy_sub_venue -t hy_materials \
            -t hy_photo_album -t hy_guide -t hy_ai_chat_log -t hy_checkin -t hy_feedback \
            > $backup_file
        
        if [ $? -eq 0 ]; then
            log_success "数据备份完成：$backup_file"
        else
            log_error "数据备份失败"
            return 1
        fi
    fi
}

# 清理现有测试数据
clean_existing_data() {
    log_warning "是否清理现有测试数据？这将删除所有会务系统相关数据！(y/n)"
    read -r clean_choice
    
    if [ "$clean_choice" = "y" ] || [ "$clean_choice" = "Y" ]; then
        log_info "正在清理现有数据..."
        
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME << EOF
-- 清理现有测试数据
DELETE FROM hy_feedback WHERE create_user = 1123598821738675201;
DELETE FROM hy_checkin WHERE create_user = 1123598821738675201;
DELETE FROM hy_ai_chat_log WHERE create_user = 1123598821738675201;
DELETE FROM hy_guide WHERE create_user = 1123598821738675201;
DELETE FROM hy_photo_album WHERE create_user = 1123598821738675201;
DELETE FROM hy_materials WHERE create_user = 1123598821738675201;
DELETE FROM hy_sub_venue WHERE create_user = 1123598821738675201;
DELETE FROM hy_live_stream WHERE create_user = 1123598821738675201;
DELETE FROM hy_agenda WHERE create_user = 1123598821738675201;
-- 注意：不删除blade_user表中的数据，避免影响系统用户
EOF
        
        if [ $? -eq 0 ]; then
            log_success "现有数据清理完成"
        else
            log_error "数据清理失败"
            return 1
        fi
    fi
}

# 执行数据插入
insert_mock_data() {
    log_info "开始插入模拟数据..."
    
    # 检查SQL文件是否存在
    if [ ! -f "insert_mock_data.sql" ]; then
        log_error "找不到 insert_mock_data.sql 文件"
        return 1
    fi
    
    # 执行SQL插入
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f insert_mock_data.sql
    
    if [ $? -eq 0 ]; then
        log_success "模拟数据插入完成"
        return 0
    else
        log_error "模拟数据插入失败"
        return 1
    fi
}

# 验证数据插入结果
verify_data() {
    log_info "验证数据插入结果..."
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME << EOF
-- 统计各表数据量
SELECT 'hy_agenda' as table_name, COUNT(*) as record_count FROM hy_agenda WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_live_stream', COUNT(*) FROM hy_live_stream WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_sub_venue', COUNT(*) FROM hy_sub_venue WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_materials', COUNT(*) FROM hy_materials WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_photo_album', COUNT(*) FROM hy_photo_album WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_guide', COUNT(*) FROM hy_guide WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_ai_chat_log', COUNT(*) FROM hy_ai_chat_log WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_checkin', COUNT(*) FROM hy_checkin WHERE create_user = 1123598821738675201
UNION ALL
SELECT 'hy_feedback', COUNT(*) FROM hy_feedback WHERE create_user = 1123598821738675201;
EOF
    
    log_success "数据验证完成"
}

# 主执行流程
main() {
    echo "=================================================="
    echo "        会务系统模拟数据插入工具"
    echo "=================================================="
    echo ""
    
    # 检查数据库连接
    if ! check_connection; then
        exit 1
    fi
    
    echo ""
    
    # 备份数据（可选）
    backup_data
    
    echo ""
    
    # 清理现有数据（可选）
    clean_existing_data
    
    echo ""
    
    # 插入模拟数据
    if insert_mock_data; then
        echo ""
        verify_data
        echo ""
        log_success "所有操作完成！"
        echo ""
        echo "数据插入统计："
        echo "- 会议议程：16条记录"
        echo "- 云直播：4条记录"
        echo "- 分会场信息：4条记录"
        echo "- 会议资料：10条记录"
        echo "- 在线相册：12条记录"
        echo "- 参会指南：8条记录"
        echo "- AI聊天记录：8条记录"
        echo "- 用户信息：5条记录"
        echo "- 签到记录：9条记录"
        echo "- 会议反馈：7条记录"
        echo ""
        echo "总计：83条测试数据记录"
    else
        log_error "数据插入失败，请检查错误信息"
        exit 1
    fi
}

# 执行主函数
main "$@"
