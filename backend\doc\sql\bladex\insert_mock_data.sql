-- 会务系统模拟数据插入SQL文件
-- 基于前端组件mock数据结构生成，确保与前端期望的数据格式匹配
-- PostgreSQL语法规范

-- ============================================================================
-- 1. 会议议程数据 (hy_agenda)
-- ============================================================================

INSERT INTO hy_agenda (date, start_time, end_time, topic, speaker, venue, description, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('2025-09-15', '08:30:00', '09:00:00', '签到注册', '会务组', '主会场大厅', '参会者签到，领取会议资料', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '09:00:00', '09:30:00', '开幕式', '主办方领导', '主会场', '大会开幕致辞，嘉宾介绍', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '09:30:00', '10:30:00', '主题演讲：数智攀登，管理跃升', '公司领导', '主会场', '探讨数字化智能化在企业管理中的创新应用', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '10:30:00', '10:45:00', '茶歇', '', '休息区', '休息时间，自由交流', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '10:45:00', '12:00:00', '圆桌讨论：企业管理创新实践', '各部门负责人', '主会场', '管理层共同探讨企业管理现代化转型路径', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '12:00:00', '13:30:00', '午餐时间', '', '餐厅', '自助午餐，网络交流', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '13:30:00', '14:30:00', '数字化管理技术分享', '信息中心主任', '技术分会场', '介绍最新的数字化管理技术和应用案例', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '14:30:00', '15:30:00', '智能化办公系统演示', '技术专家', '技术分会场', '现场演示智能化办公系统的功能和优势', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '15:30:00', '15:45:00', '下午茶歇', '', '休息区', '下午休息时间', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '15:45:00', '16:45:00', '现代企业管理理念', '管理专家', '管理分会场', '分享现代企业管理的新理念和新方法', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-15', '16:45:00', '17:30:00', '总结交流', '全体参会人员', '主会场', '第一天会议总结和经验交流', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),

-- 第二天议程
('2025-09-16', '09:00:00', '10:00:00', '经验分享：数字化管理实践', '信息中心主任', '技术分会场', '分享数字化技术在企业管理中的成功应用案例', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-16', '10:00:00', '11:00:00', '现场参观', '各部门代表', '展示区', '企业管理现代化成果展示与参观体验', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-16', '11:00:00', '11:15:00', '茶歇', '', '休息区', '上午休息时间', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-16', '11:15:00', '12:00:00', '闭幕式', '组委会', '主会场', '大会总结，颁奖典礼', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('2025-09-16', '12:00:00', '13:00:00', '午餐及自由交流', '', '餐厅', '会议结束午餐，自由交流时间', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 2. 云直播数据 (hy_live_stream)
-- ============================================================================

INSERT INTO hy_live_stream (title, url, start_time, end_time, status_text, description, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('企业管理现场会主会场直播', 'https://live.example.com/main-hall', '2025-09-15 09:00:00', '2025-09-15 17:30:00', '即将开始', '主会场全程直播，包括开幕式、主题演讲等重要环节', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('技术分会场直播', 'https://live.example.com/tech-hall', '2025-09-15 13:30:00', '2025-09-15 16:30:00', '即将开始', '技术分会场专题直播，聚焦数字化管理技术', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('管理分会场直播', 'https://live.example.com/mgmt-hall', '2025-09-15 15:45:00', '2025-09-15 16:45:00', '即将开始', '管理分会场专题直播，探讨现代企业管理理念', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('第二天全程直播', 'https://live.example.com/day2-main', '2025-09-16 09:00:00', '2025-09-16 13:00:00', '即将开始', '第二天会议全程直播，包括经验分享和闭幕式', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 3. 分会场信息数据 (hy_sub_venue)
-- ============================================================================

INSERT INTO hy_sub_venue (name, location, hy_agenda_id, manager, description, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('技术分会场', '主楼2楼A厅', 7, '张技术', '专注于数字化管理技术分享和演示，配备先进的投影设备和同声传译系统', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('管理分会场', '主楼3楼B厅', 10, '李管理', '专注于现代企业管理理念和实践分享，提供舒适的讨论环境', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('创新分会场', '主楼4楼C厅', NULL, '王创新', '专注于创新思维和方法分享，配备互动屏和创新工具', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('展示区', '主楼1楼展厅', 13, '赵展示', '企业管理现代化成果展示区，设有多个展示台和体验区', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 4. 会议资料数据 (hy_materials)
-- ============================================================================

INSERT INTO hy_materials (title, file_url, upload_time, uploader, description, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('企业管理现场会手册', '/files/conference-handbook.pdf', '2025-09-10 10:00:00', '会务组', '详细的会议指南和参会须知，包含会议流程、注意事项等', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('主题演讲PPT', '/files/keynote-presentation.pptx', '2025-09-12 14:30:00', '公司领导', '数智攀登·管理跃升主题演讲资料，包含核心理念和实施路径', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('会议议程表', '/files/agenda-schedule.pdf', '2025-09-08 16:00:00', '会务组', '详细的会议时间安排，包含所有议程和分会场信息', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('企业管理创新案例集', '/files/innovation-cases.pdf', '2025-09-11 11:20:00', '管理专家', '优秀管理实践案例汇编，包含多个成功案例分析', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('参会人员名单', '/files/attendee-list.xlsx', '2025-09-13 09:15:00', '人事部', '会议参与人员信息，包含姓名、单位、职位等', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('技术分享资料', '/files/tech-sharing.pptx', '2025-09-12 15:45:00', '信息中心主任', '数字化管理技术介绍，包含系统架构和应用案例', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('数字化转型白皮书', '/files/digital-transformation.pdf', '2025-09-09 13:30:00', '咨询顾问', '企业数字化转型指导文档，包含转型策略和实施建议', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('管理制度汇编', '/files/management-system.pdf', '2025-09-11 16:20:00', '管理部', '现代企业管理制度汇编，包含各项管理规范', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('培训教材', '/files/training-materials.pdf', '2025-09-10 14:10:00', '培训中心', '管理人员培训教材，包含理论知识和实践指导', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('会议总结模板', '/files/summary-template.docx', '2025-09-12 10:30:00', '秘书处', '会议总结报告模板，便于各部门撰写会议总结', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 5. 在线相册数据 (hy_photo_album)
-- ============================================================================

INSERT INTO hy_photo_album (title, image_url, upload_time, uploader, category, description, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('开幕式致辞', 'https://picsum.photos/800/600?random=1', '2025-09-15 09:15:00', '摄影师A', 'opening', '公司领导发表开幕致辞的精彩瞬间', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('主题演讲现场', 'https://picsum.photos/800/600?random=2', '2025-09-15 10:00:00', '摄影师A', 'speech', '数智攀登·管理跃升主题演讲现场', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('圆桌讨论', 'https://picsum.photos/800/600?random=3', '2025-09-15 11:30:00', '摄影师B', 'discussion', '企业管理创新实践圆桌讨论', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('全体合影留念', 'https://picsum.photos/800/600?random=4', '2025-09-15 17:00:00', '摄影师A', 'group', '全体参会人员合影留念', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('技术分享现场', 'https://picsum.photos/800/600?random=5', '2025-09-15 14:00:00', '摄影师C', 'speech', '数字化管理技术分享现场', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('现场参观', 'https://picsum.photos/800/600?random=6', '2025-09-16 10:30:00', '摄影师B', 'discussion', '企业管理现代化成果展示参观', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('签到现场', 'https://picsum.photos/800/600?random=7', '2025-09-15 08:45:00', '摄影师A', 'opening', '参会者签到注册现场', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('茶歇交流', 'https://picsum.photos/800/600?random=8', '2025-09-15 10:40:00', '摄影师C', 'discussion', '茶歇时间参会者交流互动', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('管理分会场', 'https://picsum.photos/800/600?random=9', '2025-09-15 16:00:00', '摄影师B', 'speech', '现代企业管理理念分享现场', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('闭幕式', 'https://picsum.photos/800/600?random=10', '2025-09-16 11:45:00', '摄影师A', 'opening', '大会闭幕式和颁奖典礼', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('午餐交流', 'https://picsum.photos/800/600?random=11', '2025-09-15 12:30:00', '摄影师C', 'discussion', '午餐时间的自由交流', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('展示区参观', 'https://picsum.photos/800/600?random=12', '2025-09-16 10:15:00', '摄影师B', 'discussion', '成果展示区参观体验', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 6. 参会指南数据 (hy_guide)
-- ============================================================================

INSERT INTO hy_guide (title, content, type, update_time, create_user, create_dept, create_time, update_user, status, is_deleted) VALUES
('会场交通指南', '地铁：2号线直达，会展中心站下车；公交：15路、28路、56路可达；自驾：提供免费停车位200个；打车：预计费用30-50元', 'transport', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('推荐住宿信息', '五星级：国际大酒店（距离500米）；四星级：商务酒店（距离800米）；经济型：快捷酒店（距离1公里）；预订电话：400-888-9999', 'accommodation', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('餐饮安排说明', '早餐：07:30-09:00 酒店自助餐厅；午餐：12:00-13:30 会场餐厅；晚餐：18:00-20:00 欢迎晚宴；茶歇：上下午各一次，提供茶点', 'dining', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('会议须知', '着装要求：商务正装，佩戴会议胸牌；签到时间：每日08:30开始签到；手机设置：会议期间请调至静音模式；资料领取：签到时领取会议资料袋', 'general', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('紧急联系方式', '会务组热线：400-123-4567；技术支持：138-0001-0001；医疗急救：120；会场安保：138-0002-0002', 'contact', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('会议设备使用', '投影设备：各会场配备高清投影仪；音响系统：专业音响设备，支持无线话筒；网络服务：提供免费WiFi，密码：Conference2025', 'equipment', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('安全须知', '消防通道：请熟悉各楼层消防通道位置；应急集合点：主楼前广场；禁止事项：会场内禁止吸烟，禁止携带危险物品', 'safety', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0),
('会议礼仪', '入场礼仪：请提前10分钟入场就座；发言礼仪：举手示意，等待主持人安排；拍照礼仪：请关闭闪光灯，避免影响他人', 'etiquette', NOW(), 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, 1, 0);

-- ============================================================================
-- 7. 会务助手问答日志数据 (hy_ai_chat_log)
-- ============================================================================

INSERT INTO hy_ai_chat_log (user_id, question, answer, create_time, create_user, create_dept, update_user, update_time, status, is_deleted) VALUES
(1, '会议时间安排', '会议时间为2025年9月15日-16日，共两天。第一天08:30开始签到，09:00正式开始。详细议程请查看"议程"页面。', '2025-09-14 10:30:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(2, '会议地点在哪', '会议地点在广东烟草大厦会议中心，地址：广州市天河区珠江新城。可乘坐地铁3号线/5号线到珠江新城站。', '2025-09-14 11:15:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(3, '如何签到', '您可以通过扫码签到或手动签到两种方式。建议提前15分钟到达会场，在签到页面完成签到流程。', '2025-09-14 14:20:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(1, '用餐安排', '会议期间提供工作餐，午餐时间为12:00-13:30。如有特殊饮食要求，请提前联系会务组。', '2025-09-14 15:45:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(4, '住宿信息', '推荐住宿：广州大酒店（步行3分钟）、珠江宾馆（步行8分钟）。详细信息请查看"参会指南"页面。', '2025-09-14 16:30:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(2, '联系方式', '会务组联系人：张先生 138-0000-0000，技术支持：李女士 139-0000-0000。如有紧急情况请及时联系。', '2025-09-14 17:10:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(5, '会议资料下载', '会议资料可在"会议资料"页面下载，包括会议手册、演讲PPT、议程表等。建议提前下载查看。', '2025-09-14 18:00:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0),
(3, '直播观看方式', '会议提供全程直播，可在"云直播"页面观看。支持多种画质选择，建议使用WiFi观看以获得更好体验。', '2025-09-14 19:20:00', 1123598821738675201, 1123598813738675201, 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 8. 用户信息数据 (blade_user) - 个人中心相关
-- ============================================================================

-- 注意：这里只插入会务系统相关的用户数据，不影响现有系统用户
INSERT INTO blade_user (tenant_id, code, account, password, name, real_name, avatar, email, phone, birthday, sex, role_id, dept_id, post_id, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
('000000', 'U001', 'zhangsan', '$2a$10$3QvGQjQvQvGQjQvGQjQvGe', 'zhangsan', '张三', '/avatar/user1.jpg', '<EMAIL>', '138-0001-0001', '1985-03-15', 1, 1, 1, 1, 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('000000', 'U002', 'lisi', '$2a$10$3QvGQjQvQvGQjQvGe', 'lisi', '李四', '/avatar/user2.jpg', '<EMAIL>', '138-0002-0002', '1987-07-22', 1, 1, 2, 2, 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('000000', 'U003', 'wangwu', '$2a$10$3QvGQjQvQvGQjQvGe', 'wangwu', '王五', '/avatar/user3.jpg', '<EMAIL>', '138-0003-0003', '1983-11-08', 1, 1, 3, 3, 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('000000', 'U004', 'zhaoliu', '$2a$10$3QvGQjQvQvGQjQvGe', 'zhaoliu', '赵六', '/avatar/user4.jpg', '<EMAIL>', '138-0004-0004', '1990-05-12', 2, 1, 4, 4, 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
('000000', 'U005', 'sunqi', '$2a$10$3QvGQjQvQvGQjQvGe', 'sunqi', '孙七', '/avatar/user5.jpg', '<EMAIL>', '138-0005-0005', '1988-09-30', 2, 1, 5, 5, 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 9. 参会人员签到记录 (hy_checkin)
-- ============================================================================

INSERT INTO hy_checkin (user_id, checkin_time, checkin_type, location, device_info, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
(1, '2025-09-15 08:35:00', 'qrcode', '主会场大厅', 'iPhone 13 Pro', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(2, '2025-09-15 08:42:00', 'manual', '主会场大厅', 'Android Phone', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(3, '2025-09-15 08:38:00', 'qrcode', '主会场大厅', 'iPad Pro', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(4, '2025-09-15 08:45:00', 'qrcode', '主会场大厅', 'Samsung Galaxy', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(5, '2025-09-15 08:40:00', 'manual', '主会场大厅', 'Huawei Mate', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
-- 第二天签到
(1, '2025-09-16 08:55:00', 'qrcode', '主会场大厅', 'iPhone 13 Pro', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(2, '2025-09-16 09:02:00', 'qrcode', '主会场大厅', 'Android Phone', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(3, '2025-09-16 08:58:00', 'manual', '主会场大厅', 'iPad Pro', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(4, '2025-09-16 09:05:00', 'qrcode', '主会场大厅', 'Samsung Galaxy', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 10. 会议评价反馈 (hy_feedback)
-- ============================================================================

INSERT INTO hy_feedback (user_id, rating, content, feedback_type, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
(1, 5, '会议组织得非常好，内容丰富，收获很大！', 'general', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(2, 4, '演讲内容很精彩，希望能提供更多的互动环节。', 'content', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(3, 5, '会场设施完善，服务周到，给会务组点赞！', 'service', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(4, 4, '直播效果不错，但希望能提供回放功能。', 'technical', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(5, 5, '资料准备充分，对工作很有指导意义。', 'content', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(1, 4, '分会场的技术分享很实用，希望能有更多这样的专题。', 'content', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(2, 5, '整体安排合理，时间控制得很好。', 'general', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);

-- ============================================================================
-- 数据插入完成提示
-- ============================================================================

-- 插入数据统计：
-- 1. 会议议程：16条记录（两天完整议程）
-- 2. 云直播：4条记录（主会场和分会场直播）
-- 3. 分会场信息：4条记录（技术、管理、创新、展示）
-- 4. 会议资料：10条记录（各类文档资料）
-- 5. 在线相册：12条记录（会议各环节照片）
-- 6. 参会指南：8条记录（交通、住宿、餐饮等指南）
-- 7. AI聊天记录：8条记录（常见问答）
-- 8. 用户信息：5条记录（测试用户）
-- 9. 签到记录：9条记录（两天签到数据）
-- 10. 会议反馈：7条记录（用户评价反馈）

-- 总计：83条测试数据记录
-- 数据涵盖会务系统所有核心功能模块
-- 确保与前端Vue组件的数据结构完全匹配
