-- H5端8大模块表结构（PostgreSQL）
-- 每个表均包含通用字段：create_user, create_dept, create_time, update_user, update_time, status, is_deleted

-- 1. 会议议程
CREATE TABLE agenda (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    date DATE NOT NULL,                   -- 会议日期
    start_time TIME NOT NULL,             -- 开始时间
    end_time TIME NOT NULL,               -- 结束时间
    topic VARCHAR(200) NOT NULL,          -- 议题/主题
    speaker VARCHAR(100),                 -- 演讲人
    venue VARCHAR(100),                   -- 会场
    description TEXT,                     -- 议程描述
    create_user BIGINT,                   -- 创建人
    create_dept BIGINT,                   -- 创建部门
    create_time TIMESTAMP(6),             -- 创建时间
    update_user BIGINT,                   -- 更新人
    update_time TIMESTAMP(6),             -- 更新时间
    status INT DEFAULT 1,                 -- 状态
    is_deleted INT DEFAULT 0              -- 是否删除
);
COMMENT ON TABLE agenda IS '会议议程表';

-- 2. 云直播
CREATE TABLE live_stream (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 直播标题
    url VARCHAR(300) NOT NULL,            -- 直播地址
    start_time TIMESTAMP NOT NULL,        -- 开始时间
    end_time TIMESTAMP,                   -- 结束时间
    status_text VARCHAR(20) DEFAULT '未开始',  -- 直播状态
    description TEXT,                     -- 直播描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE live_stream IS '云直播信息表';

-- 3. 分会场信息
CREATE TABLE sub_venue (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    name VARCHAR(100) NOT NULL,           -- 分会场名称
    location VARCHAR(200),                -- 地点
    agenda_id INTEGER REFERENCES agenda(id), -- 关联议程
    manager VARCHAR(100),                 -- 负责人
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE sub_venue IS '分会场信息表';

-- 4. 会议资料
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 资料名称
    file_url VARCHAR(300) NOT NULL,       -- 文件地址
    upload_time TIMESTAMP DEFAULT NOW(),  -- 上传时间
    uploader VARCHAR(100),                -- 上传人
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE materials IS '会议资料表';

-- 5. 在线相册
CREATE TABLE photo_album (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 相册名称
    image_url VARCHAR(300) NOT NULL,      -- 图片地址
    upload_time TIMESTAMP DEFAULT NOW(),  -- 上传时间
    uploader VARCHAR(100),                -- 上传人
    category VARCHAR(50),                 -- 分类
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE photo_album IS '会议相册表';

-- 6. 参会指南
CREATE TABLE guide (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 指南标题
    content TEXT NOT NULL,                -- 内容
    type VARCHAR(50),                     -- 类型
    update_time TIMESTAMP DEFAULT NOW(),  -- 更新时间
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    -- update_time 字段已在上方定义
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE guide IS '参会指南表';

-- 7. 会务助手
CREATE TABLE ai_chat_log (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    user_id INTEGER,                      -- 用户ID
    question TEXT NOT NULL,               -- 提问内容
    answer TEXT,                          -- AI回复
    create_time TIMESTAMP DEFAULT NOW(),  -- 提问时间
    create_user BIGINT,
    create_dept BIGINT,
    -- create_time 字段已在上方定义
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE ai_chat_log IS 'AI会务助手问答日志表';

-- 8. 个人中心
CREATE TABLE user_profile (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    name VARCHAR(100) NOT NULL,           -- 姓名
    phone VARCHAR(20),                    -- 手机号
    email VARCHAR(100),                   -- 邮箱
    avatar VARCHAR(300),                  -- 头像
    company VARCHAR(100),                 -- 单位
    position VARCHAR(100),                -- 职位
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE user_profile IS '用户个人信息表';

CREATE TABLE attendance_record (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    user_id INTEGER REFERENCES user_profile(id), -- 用户ID
    agenda_id INTEGER REFERENCES agenda(id),     -- 议程ID
    checkin_time TIMESTAMP,               -- 签到时间
    status_text VARCHAR(20),              -- 签到状态
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE attendance_record IS '参会签到记录表'; 