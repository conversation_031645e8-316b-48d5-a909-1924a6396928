-- -----------------------------------
-- 拓展代码生成配置表
-- -----------------------------------
ALTER TABLE "blade_code_setting"
    ADD COLUMN "name" varchar(32),
    ADD COLUMN "code" varchar(16),
    ADD COLUMN "category" int4 DEFAULT 1;

COMMENT ON COLUMN "blade_code_setting"."name" IS '名称';

COMMENT ON COLUMN "blade_code_setting"."code" IS '编号';

COMMENT ON COLUMN "blade_code_setting"."category" IS '分类:1 默认配置 2 表单设计';
