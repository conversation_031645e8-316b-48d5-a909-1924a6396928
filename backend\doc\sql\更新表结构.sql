-- 2025-07-29 用户日程信息表
-- 用于存储用户的日程、用餐、住宿信息，给个人中心使用

DROP TABLE IF EXISTS "hy_user_schedule";
CREATE TABLE hy_user_schedule (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    schedule_content TEXT,                    -- 日程信息（富文本）
    dining_info TEXT,                        -- 用餐信息（JSON格式）
    accommodation_info TEXT,                 -- 住宿信息（JSON格式）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_user_schedule IS '用户日程信息表';
COMMENT ON COLUMN hy_user_schedule.id IS '主键，自增';
COMMENT ON COLUMN hy_user_schedule.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_user_schedule.schedule_content IS '日程信息（富文本）';
COMMENT ON COLUMN hy_user_schedule.dining_info IS '用餐信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.accommodation_info IS '住宿信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.create_user IS '创建人';
COMMENT ON COLUMN hy_user_schedule.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_schedule.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_schedule.update_user IS '更新人';
COMMENT ON COLUMN hy_user_schedule.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_schedule.status IS '状态';
COMMENT ON COLUMN hy_user_schedule.is_deleted IS '是否删除';

-- 创建索引
CREATE INDEX idx_hy_user_schedule_user_id ON hy_user_schedule(user_id);
CREATE INDEX idx_hy_user_schedule_status ON hy_user_schedule(status);
CREATE INDEX idx_hy_user_schedule_is_deleted ON hy_user_schedule(is_deleted);

-- 2025-07-30 为hy_sub_venue表添加视频URL和PDF URL字段
ALTER TABLE hy_sub_venue
    ADD COLUMN video_url VARCHAR(500),
    ADD COLUMN pdf_url VARCHAR(500);

COMMENT ON COLUMN hy_sub_venue.video_url IS '视频URL';
COMMENT ON COLUMN hy_sub_venue.pdf_url IS 'PDF文档URL';

