package org.springblade.modules.desk.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.desk.pojo.entity.Notice;

/**
 * 通知公告视图类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "公告实体VO")
public class NoticeVO extends Notice {

	@Schema(description = "通知类型名")
	private String categoryName;

	@Schema(description = "租户编号")
	private String tenantId;

}
