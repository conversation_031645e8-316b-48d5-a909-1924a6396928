<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.develop.mapper.ModelPrototypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="modelPrototypeResultMap" type="org.springblade.modules.develop.pojo.entity.ModelPrototype">
        <id column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="jdbc_name" property="jdbcName"/>
        <result column="jdbc_type" property="jdbcType"/>
        <result column="jdbc_comment" property="jdbcComment"/>
        <result column="property_type" property="propertyType"/>
        <result column="property_entity" property="propertyEntity"/>
        <result column="property_name" property="propertyName"/>
        <result column="is_form" property="isForm"/>
        <result column="is_row" property="isRow"/>
        <result column="component_type" property="componentType"/>
        <result column="dict_code" property="dictCode"/>
        <result column="is_required" property="isRequired"/>
        <result column="is_list" property="isList"/>
        <result column="is_query" property="isQuery"/>
        <result column="query_type" property="queryType"/>
    </resultMap>


    <select id="selectModelPrototypePage" resultMap="modelPrototypeResultMap">
        select * from blade_model_prototype where is_deleted = 0
    </select>

</mapper>
