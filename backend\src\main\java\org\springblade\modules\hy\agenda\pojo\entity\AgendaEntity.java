/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.agenda.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 会议议程表 实体类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@TableName("hy_agenda")
@Schema(description = "Agenda对象")
@EqualsAndHashCode(callSuper = true)
public class AgendaEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 开始时间
	 */
	@Schema(description = "开始时间")
	private LocalTime startTime;
	/**
	 * 结束时间
	 */
	@Schema(description = "结束时间")
	private LocalTime endTime;
	/**
	 * 议题/主题
	 */
	@Schema(description = "议题/主题")
	private String topic;
	/**
	 * 演讲人
	 */
	@Schema(description = "演讲人")
	private String speaker;
	/**
	 * 会场
	 */
	@Schema(description = "会场")
	private String venue;
	/**
	 * 议程描述
	 */
	@Schema(description = "议程描述")
	private String description;
	/**
	 * 会议日期
	 */
	@Schema(description = "会议日期")
	private LocalDate date;

}
