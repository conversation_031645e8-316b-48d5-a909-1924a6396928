/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.guide.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.guide.pojo.entity.GuideEntity;
import org.springblade.modules.hy.guide.pojo.vo.GuideVO;
import org.springblade.modules.hy.guide.excel.GuideExcel;
import org.springblade.modules.hy.guide.wrapper.GuideWrapper;
import org.springblade.modules.hy.guide.service.IGuideService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 参会指南表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/guide")
@Tag(name = "参会指南表", description = "参会指南表接口")
public class GuideController extends BladeController {

	private final IGuideService guideService;

	/**
	 * 参会指南表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入guide")
	public R<GuideVO> detail(GuideEntity guide) {
		GuideEntity detail = guideService.getOne(Condition.getQueryWrapper(guide));
		return R.data(GuideWrapper.build().entityVO(detail));
	}
	/**
	 * 参会指南表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入guide")
	public R<IPage<GuideVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> guide, Query query) {
		IPage<GuideEntity> pages = guideService.page(Condition.getPage(query), Condition.getQueryWrapper(guide, GuideEntity.class));
		return R.data(GuideWrapper.build().pageVO(pages));
	}

	/**
	 * 参会指南表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入guide")
	public R<IPage<GuideVO>> page(GuideVO guide, Query query) {
		IPage<GuideVO> pages = guideService.selectGuidePage(Condition.getPage(query), guide);
		return R.data(pages);
	}

	/**
	 * 参会指南表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入guide")
	public R save(@Valid @RequestBody GuideEntity guide) {
		return R.status(guideService.save(guide));
	}

	/**
	 * 参会指南表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入guide")
	public R update(@Valid @RequestBody GuideEntity guide) {
		return R.status(guideService.updateById(guide));
	}

	/**
	 * 参会指南表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入guide")
	public R submit(@Valid @RequestBody GuideEntity guide) {
		return R.status(guideService.saveOrUpdate(guide));
	}

	/**
	 * 参会指南表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(guideService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-guide")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入guide")
	public void exportGuide(@Parameter(hidden = true) @RequestParam Map<String, Object> guide, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuideEntity> queryWrapper = Condition.getQueryWrapper(guide, GuideEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Guide::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(GuideEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuideExcel> list = guideService.exportGuide(queryWrapper);
		ExcelUtil.export(response, "参会指南表数据" + DateUtil.time(), "参会指南表数据表", list, GuideExcel.class);
	}

}
