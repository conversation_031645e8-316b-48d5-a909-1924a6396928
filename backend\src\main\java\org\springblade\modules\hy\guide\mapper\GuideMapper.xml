<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.guide.mapper.GuideMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="guideResultMap" type="org.springblade.modules.hy.guide.pojo.entity.GuideEntity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuidePage" resultMap="guideResultMap">
        select * from hy_guide where is_deleted = 0
    </select>


    <select id="exportGuide" resultType="org.springblade.modules.hy.guide.excel.GuideExcel">
        SELECT * FROM hy_guide ${ew.customSqlSegment}
    </select>

</mapper>
