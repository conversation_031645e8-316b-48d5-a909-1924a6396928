/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.guide.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.hy.guide.pojo.entity.GuideEntity;
import org.springblade.modules.hy.guide.pojo.vo.GuideVO;
import org.springblade.modules.hy.guide.excel.GuideExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 参会指南表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public interface IGuideService extends BaseService<GuideEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param guide 查询参数
	 * @return IPage<GuideVO>
	 */
	IPage<GuideVO> selectGuidePage(IPage<GuideVO> page, GuideVO guide);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<GuideExcel>
	 */
	List<GuideExcel> exportGuide(Wrapper<GuideEntity> queryWrapper);

}
