/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.guide.service.impl;

import org.springblade.modules.hy.guide.pojo.entity.GuideEntity;
import org.springblade.modules.hy.guide.pojo.vo.GuideVO;
import org.springblade.modules.hy.guide.excel.GuideExcel;
import org.springblade.modules.hy.guide.mapper.GuideMapper;
import org.springblade.modules.hy.guide.service.IGuideService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 参会指南表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class GuideServiceImpl extends BaseServiceImpl<GuideMapper, GuideEntity> implements IGuideService {

	@Override
	public IPage<GuideVO> selectGuidePage(IPage<GuideVO> page, GuideVO guide) {
		return page.setRecords(baseMapper.selectGuidePage(page, guide));
	}


	@Override
	public List<GuideExcel> exportGuide(Wrapper<GuideEntity> queryWrapper) {
		List<GuideExcel> guideList = baseMapper.exportGuide(queryWrapper);
		//guideList.forEach(guide -> {
		//	guide.setTypeName(DictCache.getValue(DictEnum.YES_NO, Guide.getType()));
		//});
		return guideList;
	}

}
