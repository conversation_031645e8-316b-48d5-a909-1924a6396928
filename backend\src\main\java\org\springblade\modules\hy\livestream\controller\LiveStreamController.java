/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.livestream.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.livestream.pojo.entity.LiveStreamEntity;
import org.springblade.modules.hy.livestream.pojo.vo.LiveStreamVO;
import org.springblade.modules.hy.livestream.excel.LiveStreamExcel;
import org.springblade.modules.hy.livestream.wrapper.LiveStreamWrapper;
import org.springblade.modules.hy.livestream.service.ILiveStreamService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 云直播信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/liveStream")
@Tag(name = "云直播管理", description = "会议云直播信息的增删改查操作，包括直播详情查询、分页列表、新增修改、删除和数据导出等功能")
public class LiveStreamController extends BladeController {

	private final ILiveStreamService liveStreamService;

	/**
	 * 云直播详情查询
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取云直播详情", description = "根据直播ID或其他条件查询单个云直播的详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<LiveStreamVO> detail(@Parameter(description = "云直播实体对象，包含查询条件", required = true) LiveStreamEntity liveStream) {
		LiveStreamEntity detail = liveStreamService.getOne(Condition.getQueryWrapper(liveStream));
		return R.data(LiveStreamWrapper.build().entityVO(detail));
	}
	/**
	 * 云直播分页查询
	 */
	@GetMapping("/list")
	@Parameters({
		@Parameter(name = "title", description = "直播标题", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "status", description = "直播状态", in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "startTime", description = "开始时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time")),
		@Parameter(name = "endTime", description = "结束时间", in = ParameterIn.QUERY, schema = @Schema(type = "string", format = "date-time")),
		@Parameter(name = "current", description = "当前页码", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "1")),
		@Parameter(name = "size", description = "每页条数", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
	})
	@ApiOperationSupport(order = 2)
	@Operation(summary = "云直播分页列表", description = "分页查询云直播信息，支持按标题、状态、时间等条件筛选")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<IPage<LiveStreamVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> liveStream,
									   @Parameter(description = "分页查询参数") Query query) {
		IPage<LiveStreamEntity> pages = liveStreamService.page(Condition.getPage(query), Condition.getQueryWrapper(liveStream, LiveStreamEntity.class));
		return R.data(LiveStreamWrapper.build().pageVO(pages));
	}

	/**
	 * 云直播信息表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入liveStream")
	public R<IPage<LiveStreamVO>> page(LiveStreamVO liveStream, Query query) {
		IPage<LiveStreamVO> pages = liveStreamService.selectLiveStreamPage(Condition.getPage(query), liveStream);
		return R.data(pages);
	}

	/**
	 * 云直播信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入liveStream")
	public R save(@Valid @RequestBody LiveStreamEntity liveStream) {
		return R.status(liveStreamService.save(liveStream));
	}

	/**
	 * 云直播信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入liveStream")
	public R update(@Valid @RequestBody LiveStreamEntity liveStream) {
		return R.status(liveStreamService.updateById(liveStream));
	}

	/**
	 * 云直播信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入liveStream")
	public R submit(@Valid @RequestBody LiveStreamEntity liveStream) {
		return R.status(liveStreamService.saveOrUpdate(liveStream));
	}

	/**
	 * 云直播信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(liveStreamService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-liveStream")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入liveStream")
	public void exportLiveStream(@Parameter(hidden = true) @RequestParam Map<String, Object> liveStream, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<LiveStreamEntity> queryWrapper = Condition.getQueryWrapper(liveStream, LiveStreamEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(LiveStream::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(LiveStreamEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<LiveStreamExcel> list = liveStreamService.exportLiveStream(queryWrapper);
		ExcelUtil.export(response, "云直播信息表数据" + DateUtil.time(), "云直播信息表数据表", list, LiveStreamExcel.class);
	}

}
