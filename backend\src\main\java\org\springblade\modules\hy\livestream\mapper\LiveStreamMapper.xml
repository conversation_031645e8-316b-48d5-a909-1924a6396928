<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.livestream.mapper.LiveStreamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="liveStreamResultMap" type="org.springblade.modules.hy.livestream.pojo.entity.LiveStreamEntity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="speaker" property="speaker"/>
        <result column="url" property="url"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status_text" property="statusText"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectLiveStreamPage" resultMap="liveStreamResultMap">
        select * from hy_live_stream where is_deleted = 0
    </select>


    <select id="exportLiveStream" resultType="org.springblade.modules.hy.livestream.excel.LiveStreamExcel">
        SELECT * FROM hy_live_stream ${ew.customSqlSegment}
    </select>

</mapper>
