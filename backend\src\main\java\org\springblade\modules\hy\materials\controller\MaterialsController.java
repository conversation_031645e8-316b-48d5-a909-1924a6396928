/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.materials.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.materials.pojo.entity.MaterialsEntity;
import org.springblade.modules.hy.materials.pojo.vo.MaterialsVO;
import org.springblade.modules.hy.materials.excel.MaterialsExcel;
import org.springblade.modules.hy.materials.wrapper.MaterialsWrapper;
import org.springblade.modules.hy.materials.service.IMaterialsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 会议资料表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/materials")
@Tag(name = "会议资料管理", description = "会议资料信息的增删改查操作，包括资料详情查询、分页列表、新增修改、删除和数据导出等功能")
public class MaterialsController extends BladeController {

	private final IMaterialsService materialsService;

	/**
	 * 会议资料详情查询
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取会议资料详情", description = "根据资料ID或其他条件查询单个会议资料的详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<MaterialsVO> detail(@Parameter(description = "会议资料实体对象，包含查询条件", required = true) MaterialsEntity materials) {
		MaterialsEntity detail = materialsService.getOne(Condition.getQueryWrapper(materials));
		return R.data(MaterialsWrapper.build().entityVO(detail));
	}
	/**
	 * 会议资料表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入materials")
	public R<IPage<MaterialsVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> materials, Query query) {
		IPage<MaterialsEntity> pages = materialsService.page(Condition.getPage(query), Condition.getQueryWrapper(materials, MaterialsEntity.class));
		return R.data(MaterialsWrapper.build().pageVO(pages));
	}

	/**
	 * 会议资料表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入materials")
	public R<IPage<MaterialsVO>> page(MaterialsVO materials, Query query) {
		IPage<MaterialsVO> pages = materialsService.selectMaterialsPage(Condition.getPage(query), materials);
		return R.data(pages);
	}

	/**
	 * 会议资料表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入materials")
	public R save(@Valid @RequestBody MaterialsEntity materials) {
		return R.status(materialsService.save(materials));
	}

	/**
	 * 会议资料表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入materials")
	public R update(@Valid @RequestBody MaterialsEntity materials) {
		return R.status(materialsService.updateById(materials));
	}

	/**
	 * 会议资料表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入materials")
	public R submit(@Valid @RequestBody MaterialsEntity materials) {
		return R.status(materialsService.saveOrUpdate(materials));
	}

	/**
	 * 会议资料表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(materialsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-materials")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入materials")
	public void exportMaterials(@Parameter(hidden = true) @RequestParam Map<String, Object> materials, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<MaterialsEntity> queryWrapper = Condition.getQueryWrapper(materials, MaterialsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Materials::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(MaterialsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<MaterialsExcel> list = materialsService.exportMaterials(queryWrapper);
		ExcelUtil.export(response, "会议资料表数据" + DateUtil.time(), "会议资料表数据表", list, MaterialsExcel.class);
	}

}
