<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.materials.mapper.MaterialsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="materialsResultMap" type="org.springblade.modules.hy.materials.pojo.entity.MaterialsEntity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="file_url" property="fileUrl"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="uploader" property="uploader"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectMaterialsPage" resultMap="materialsResultMap">
        select * from hy_materials where is_deleted = 0
    </select>


    <select id="exportMaterials" resultType="org.springblade.modules.hy.materials.excel.MaterialsExcel">
        SELECT * FROM hy_materials ${ew.customSqlSegment}
    </select>

</mapper>
