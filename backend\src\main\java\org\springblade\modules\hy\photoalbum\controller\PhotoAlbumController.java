/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.photoalbum.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.photoalbum.pojo.entity.PhotoAlbumEntity;
import org.springblade.modules.hy.photoalbum.pojo.vo.PhotoAlbumVO;
import org.springblade.modules.hy.photoalbum.excel.PhotoAlbumExcel;
import org.springblade.modules.hy.photoalbum.wrapper.PhotoAlbumWrapper;
import org.springblade.modules.hy.photoalbum.service.IPhotoAlbumService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 会议相册表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/photoAlbum")
@Tag(name = "会议相册表", description = "会议相册表接口")
public class PhotoAlbumController extends BladeController {

	private final IPhotoAlbumService photoAlbumService;

	/**
	 * 会议相册表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入photoAlbum")
	public R<PhotoAlbumVO> detail(PhotoAlbumEntity photoAlbum) {
		PhotoAlbumEntity detail = photoAlbumService.getOne(Condition.getQueryWrapper(photoAlbum));
		return R.data(PhotoAlbumWrapper.build().entityVO(detail));
	}
	/**
	 * 会议相册表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入photoAlbum")
	public R<IPage<PhotoAlbumVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> photoAlbum, Query query) {
		IPage<PhotoAlbumEntity> pages = photoAlbumService.page(Condition.getPage(query), Condition.getQueryWrapper(photoAlbum, PhotoAlbumEntity.class));
		return R.data(PhotoAlbumWrapper.build().pageVO(pages));
	}

	/**
	 * 会议相册表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入photoAlbum")
	public R<IPage<PhotoAlbumVO>> page(PhotoAlbumVO photoAlbum, Query query) {
		IPage<PhotoAlbumVO> pages = photoAlbumService.selectPhotoAlbumPage(Condition.getPage(query), photoAlbum);
		return R.data(pages);
	}

	/**
	 * 会议相册表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入photoAlbum")
	public R save(@Valid @RequestBody PhotoAlbumEntity photoAlbum) {
		return R.status(photoAlbumService.save(photoAlbum));
	}

	/**
	 * 会议相册表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入photoAlbum")
	public R update(@Valid @RequestBody PhotoAlbumEntity photoAlbum) {
		return R.status(photoAlbumService.updateById(photoAlbum));
	}

	/**
	 * 会议相册表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入photoAlbum")
	public R submit(@Valid @RequestBody PhotoAlbumEntity photoAlbum) {
		return R.status(photoAlbumService.saveOrUpdate(photoAlbum));
	}

	/**
	 * 会议相册表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(photoAlbumService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-photoAlbum")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入photoAlbum")
	public void exportPhotoAlbum(@Parameter(hidden = true) @RequestParam Map<String, Object> photoAlbum, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PhotoAlbumEntity> queryWrapper = Condition.getQueryWrapper(photoAlbum, PhotoAlbumEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(PhotoAlbum::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(PhotoAlbumEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PhotoAlbumExcel> list = photoAlbumService.exportPhotoAlbum(queryWrapper);
		ExcelUtil.export(response, "会议相册表数据" + DateUtil.time(), "会议相册表数据表", list, PhotoAlbumExcel.class);
	}

}
