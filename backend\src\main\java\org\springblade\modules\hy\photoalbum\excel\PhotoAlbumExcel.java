/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.photoalbum.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 会议相册表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class PhotoAlbumExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键，自增
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键，自增")
	private Integer id;
	/**
	 * 相册名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("相册名称")
	private String title;
	/**
	 * 图片地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("图片地址")
	private String imageUrl;
	/**
	 * 上传时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("上传时间")
	private Date uploadTime;
	/**
	 * 上传人
	 */
	@ColumnWidth(20)
	@ExcelProperty("上传人")
	private String uploader;
	/**
	 * 分类
	 */
	@ColumnWidth(20)
	@ExcelProperty("分类")
	private String category;
	/**
	 * 描述
	 */
	@ColumnWidth(20)
	@ExcelProperty("描述")
	private String description;
	/**
	 * 是否删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否删除")
	private Integer isDeleted;

}
