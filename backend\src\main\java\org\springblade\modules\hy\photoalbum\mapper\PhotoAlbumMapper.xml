<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.photoalbum.mapper.PhotoAlbumMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="photoAlbumResultMap" type="org.springblade.modules.hy.photoalbum.pojo.entity.PhotoAlbumEntity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="image_url" property="imageUrl"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="uploader" property="uploader"/>
        <result column="category" property="category"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectPhotoAlbumPage" resultMap="photoAlbumResultMap">
        select * from hy_photo_album where is_deleted = 0
    </select>


    <select id="exportPhotoAlbum" resultType="org.springblade.modules.hy.photoalbum.excel.PhotoAlbumExcel">
        SELECT * FROM hy_photo_album ${ew.customSqlSegment}
    </select>

</mapper>
