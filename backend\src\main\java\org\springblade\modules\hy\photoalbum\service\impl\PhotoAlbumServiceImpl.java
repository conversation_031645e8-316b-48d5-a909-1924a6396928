/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.photoalbum.service.impl;

import org.springblade.modules.hy.photoalbum.pojo.entity.PhotoAlbumEntity;
import org.springblade.modules.hy.photoalbum.pojo.vo.PhotoAlbumVO;
import org.springblade.modules.hy.photoalbum.excel.PhotoAlbumExcel;
import org.springblade.modules.hy.photoalbum.mapper.PhotoAlbumMapper;
import org.springblade.modules.hy.photoalbum.service.IPhotoAlbumService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 会议相册表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class PhotoAlbumServiceImpl extends BaseServiceImpl<PhotoAlbumMapper, PhotoAlbumEntity> implements IPhotoAlbumService {

	@Override
	public IPage<PhotoAlbumVO> selectPhotoAlbumPage(IPage<PhotoAlbumVO> page, PhotoAlbumVO photoAlbum) {
		return page.setRecords(baseMapper.selectPhotoAlbumPage(page, photoAlbum));
	}


	@Override
	public List<PhotoAlbumExcel> exportPhotoAlbum(Wrapper<PhotoAlbumEntity> queryWrapper) {
		List<PhotoAlbumExcel> photoAlbumList = baseMapper.exportPhotoAlbum(queryWrapper);
		//photoAlbumList.forEach(photoAlbum -> {
		//	photoAlbum.setTypeName(DictCache.getValue(DictEnum.YES_NO, PhotoAlbum.getType()));
		//});
		return photoAlbumList;
	}

}
