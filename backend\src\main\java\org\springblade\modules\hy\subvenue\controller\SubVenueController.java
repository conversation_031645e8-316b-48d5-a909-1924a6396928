/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.subvenue.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.subvenue.pojo.entity.SubVenueEntity;
import org.springblade.modules.hy.subvenue.pojo.vo.SubVenueVO;
import org.springblade.modules.hy.subvenue.excel.SubVenueExcel;
import org.springblade.modules.hy.subvenue.wrapper.SubVenueWrapper;
import org.springblade.modules.hy.subvenue.service.ISubVenueService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 分会场信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/subVenue")
@Tag(name = "分会场信息表", description = "分会场信息表接口")
public class SubVenueController extends BladeController {

	private final ISubVenueService subVenueService;

	/**
	 * 分会场信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入subVenue")
	public R<SubVenueVO> detail(SubVenueEntity subVenue) {
		SubVenueEntity detail = subVenueService.getOne(Condition.getQueryWrapper(subVenue));
		return R.data(SubVenueWrapper.build().entityVO(detail));
	}
	/**
	 * 分会场信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入subVenue")
	public R<IPage<SubVenueVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> subVenue, Query query) {
		IPage<SubVenueEntity> pages = subVenueService.page(Condition.getPage(query), Condition.getQueryWrapper(subVenue, SubVenueEntity.class));
		return R.data(SubVenueWrapper.build().pageVO(pages));
	}

	/**
	 * 分会场信息表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入subVenue")
	public R<IPage<SubVenueVO>> page(SubVenueVO subVenue, Query query) {
		IPage<SubVenueVO> pages = subVenueService.selectSubVenuePage(Condition.getPage(query), subVenue);
		return R.data(pages);
	}

	/**
	 * 分会场信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入subVenue")
	public R save(@Valid @RequestBody SubVenueEntity subVenue) {
		return R.status(subVenueService.save(subVenue));
	}

	/**
	 * 分会场信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入subVenue")
	public R update(@Valid @RequestBody SubVenueEntity subVenue) {
		return R.status(subVenueService.updateById(subVenue));
	}

	/**
	 * 分会场信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入subVenue")
	public R submit(@Valid @RequestBody SubVenueEntity subVenue) {
		return R.status(subVenueService.saveOrUpdate(subVenue));
	}

	/**
	 * 分会场信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(subVenueService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-subVenue")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入subVenue")
	public void exportSubVenue(@Parameter(hidden = true) @RequestParam Map<String, Object> subVenue, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SubVenueEntity> queryWrapper = Condition.getQueryWrapper(subVenue, SubVenueEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SubVenue::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SubVenueEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SubVenueExcel> list = subVenueService.exportSubVenue(queryWrapper);
		ExcelUtil.export(response, "分会场信息表数据" + DateUtil.time(), "分会场信息表数据表", list, SubVenueExcel.class);
	}

}
