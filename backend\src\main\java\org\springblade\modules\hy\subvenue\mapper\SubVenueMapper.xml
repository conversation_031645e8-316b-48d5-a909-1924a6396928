<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.subvenue.mapper.SubVenueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subVenueResultMap" type="org.springblade.modules.hy.subvenue.pojo.entity.SubVenueEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="location" property="location"/>
        <result column="hy_agenda_id" property="hyAgendaId"/>
        <result column="manager" property="manager"/>
        <result column="description" property="description"/>
        <result column="video_url" property="videoUrl"/>
        <result column="pdf_url" property="pdfUrl"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectSubVenuePage" resultMap="subVenueResultMap">
        select * from hy_sub_venue where is_deleted = 0
    </select>


    <select id="exportSubVenue" resultType="org.springblade.modules.hy.subvenue.excel.SubVenueExcel">
        SELECT * FROM hy_sub_venue ${ew.customSqlSegment}
    </select>

</mapper>
