/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.subvenue.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 分会场信息表 实体类
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@TableName("hy_sub_venue")
@Schema(description = "SubVenue对象")
@EqualsAndHashCode(callSuper = true)
public class SubVenueEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 分会场名称
	 */
	@Schema(description = "分会场名称")
	private String name;
	/**
	 * 地点
	 */
	@Schema(description = "地点")
	private String location;
	/**
	 * 关联议程
	 */
	@Schema(description = "关联议程")
	private Integer hyAgendaId;
	/**
	 * 负责人
	 */
	@Schema(description = "负责人")
	private String manager;
	/**
	 * 描述
	 */
	@Schema(description = "描述")
	private String description;
	/**
	 * 视频URL
	 */
	@Schema(description = "视频URL")
	private String videoUrl;
	/**
	 * PDF文档URL
	 */
	@Schema(description = "PDF文档URL")
	private String pdfUrl;

}
