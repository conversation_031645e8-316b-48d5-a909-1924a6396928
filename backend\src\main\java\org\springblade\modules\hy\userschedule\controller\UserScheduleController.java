/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;


import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.wrapper.UserScheduleWrapper;
import org.springblade.modules.hy.userschedule.service.IUserScheduleService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

import static org.springblade.core.secure.utils.AuthUtil.getUserId;

/**
 * 用户日程信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/user-schedule")
@Tag(name = "用户日程信息表", description = "用户日程信息表接口")
public class UserScheduleController extends BladeController {

	private final IUserScheduleService userScheduleService;
	private final IUserService userService;

	/**
	 * 用户日程信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入userSchedule")
	public R<UserScheduleVO> detail(UserScheduleEntity userSchedule) {
		UserScheduleEntity detail = userScheduleService.getOne(Condition.getQueryWrapper(userSchedule));
		return R.data(UserScheduleWrapper.build().entityVO(detail));
	}

	/**
	 * 根据用户ID获取日程信息
	 */
	@GetMapping("/by-user/{userId}")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "根据用户ID获取日程信息", description = "传入userId")
	public R<UserScheduleVO> getByUserId(@PathVariable Long userId) {
		UserScheduleVO userSchedule = userScheduleService.getByUserId(userId);
		return R.data(userSchedule);
	}

	/**
	 * 获取当前用户的日程信息
	 */
	@GetMapping("/current")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "获取当前用户的日程信息")
	public R<UserScheduleVO> getCurrentUserSchedule() {
		Long userId = getUserId();
		UserScheduleVO userSchedule = userScheduleService.getByUserId(userId);
		return R.data(userSchedule);
	}

	/**
	 * 用户日程信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "分页", description = "传入userSchedule")
	public R<IPage<UserScheduleVO>> list(UserScheduleVO userSchedule, Query query) {
		IPage<UserScheduleVO> pages = userScheduleService.selectUserSchedulePage(Condition.getPage(query), userSchedule);
		return R.data(pages);
	}

	/**
	 * 用户日程信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "新增", description = "传入userSchedule")
	public R save(@Valid @RequestBody UserScheduleEntity userSchedule) {
		return R.status(userScheduleService.save(userSchedule));
	}

	/**
	 * 用户日程信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "修改", description = "传入userSchedule")
	public R update(@Valid @RequestBody UserScheduleEntity userSchedule) {
		return R.status(userScheduleService.updateById(userSchedule));
	}

	/**
	 * 保存或更新当前用户的日程信息
	 */
	@PostMapping("/save-current")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "保存或更新当前用户的日程信息", description = "传入userSchedule")
	public R saveCurrentUserSchedule(@Valid @RequestBody UserScheduleEntity userSchedule) {
		userSchedule.setUserId(getUserId());
		return R.status(userScheduleService.saveOrUpdateByUserId(userSchedule));
	}

	/**
	 * 用户日程信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "新增或修改", description = "传入userSchedule")
	public R submit(@Valid @RequestBody UserScheduleEntity userSchedule) {
		// 根据用户姓名查找用户ID
		if (StringUtil.isNotBlank(userSchedule.getUserRealName())) {
			LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
			userQuery.eq(User::getRealName, userSchedule.getUserRealName());
			User user = userService.getOne(userQuery);
			if (user != null) {
				userSchedule.setUserId(user.getId());
			}
		}
		return R.status(userScheduleService.saveOrUpdate(userSchedule));
	}

	/**
	 * 用户日程信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@RequestParam String ids) {
		return R.status(userScheduleService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 用户日程信息表 导出
	 */
	@GetMapping("/export-user-schedule")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导出", description = "传入userSchedule")
	public void exportUserSchedule(UserScheduleEntity userSchedule, HttpServletResponse response) {
		QueryWrapper<UserScheduleEntity> queryWrapper = Condition.getQueryWrapper(userSchedule);
		// 添加关联查询条件
		if (StringUtil.isNotBlank(userSchedule.getUserRealName())) {
			queryWrapper.apply("EXISTS (SELECT 1 FROM blade_user u WHERE u.id = hy_user_schedule.user_id AND u.real_name LIKE {0})", "%" + userSchedule.getUserRealName() + "%");
		}
		// 支持按用户ID查询
		if (userSchedule.getUserId() != null) {
			queryWrapper.eq("user_id", userSchedule.getUserId());
		}
		List<UserScheduleExcel> list = userScheduleService.exportUserSchedule(queryWrapper);
		ExcelUtil.export(response, "用户日程信息表数据", "用户日程信息表数据", list, UserScheduleExcel.class);
	}

}
