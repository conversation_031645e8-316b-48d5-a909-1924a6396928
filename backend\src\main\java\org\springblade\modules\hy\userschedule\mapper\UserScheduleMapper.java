/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import java.util.List;

/**
 * 用户日程信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface UserScheduleMapper extends BaseMapper<UserScheduleEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userSchedule
	 * @return
	 */
	List<UserScheduleVO> selectUserSchedulePage(IPage page, UserScheduleVO userSchedule);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper);

	/**
	 * 根据用户ID查询用户日程信息
	 *
	 * @param userId
	 * @return
	 */
	UserScheduleVO selectByUserId(Long userId);

}
