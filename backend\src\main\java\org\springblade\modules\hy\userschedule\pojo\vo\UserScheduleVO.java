/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import java.io.Serial;

/**
 * 用户日程信息表 视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "UserScheduleVO对象")
public class UserScheduleVO extends UserScheduleEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户名称
	 */
	@Schema(description = "用户名称")
	private String userName;

	/**
	 * 用户真实姓名
	 */
	@Schema(description = "用户真实姓名")
	private String userRealName;

	/**
	 * 用户邮箱
	 */
	@Schema(description = "用户邮箱")
	private String userEmail;

	/**
	 * 用户手机号
	 */
	@Schema(description = "用户手机号")
	private String userPhone;

}
