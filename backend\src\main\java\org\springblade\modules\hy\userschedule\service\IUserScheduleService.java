/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;

/**
 * 用户日程信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IUserScheduleService extends BaseService<UserScheduleEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userSchedule
	 * @return
	 */
	IPage<UserScheduleVO> selectUserSchedulePage(IPage<UserScheduleVO> page, UserScheduleVO userSchedule);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper);

	/**
	 * 根据用户ID查询用户日程信息
	 *
	 * @param userId
	 * @return
	 */
	UserScheduleVO getByUserId(Long userId);

	/**
	 * 保存或更新用户日程信息
	 *
	 * @param userSchedule
	 * @return
	 */
	boolean saveOrUpdateByUserId(UserScheduleEntity userSchedule);

}
