/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.service.impl;

import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import org.springblade.modules.hy.userschedule.pojo.excel.UserScheduleExcel;
import org.springblade.modules.hy.userschedule.mapper.UserScheduleMapper;
import org.springblade.modules.hy.userschedule.service.IUserScheduleService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**
 * 用户日程信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class UserScheduleServiceImpl extends BaseServiceImpl<UserScheduleMapper, UserScheduleEntity> implements IUserScheduleService {

	@Override
	public IPage<UserScheduleVO> selectUserSchedulePage(IPage<UserScheduleVO> page, UserScheduleVO userSchedule) {
		return page.setRecords(baseMapper.selectUserSchedulePage(page, userSchedule));
	}

	@Override
	public List<UserScheduleExcel> exportUserSchedule(Wrapper<UserScheduleEntity> queryWrapper) {
		return baseMapper.exportUserSchedule(queryWrapper);
	}

	@Override
	public UserScheduleVO getByUserId(Long userId) {
		return baseMapper.selectByUserId(userId);
	}

	@Override
	public boolean saveOrUpdateByUserId(UserScheduleEntity userSchedule) {
		// 根据用户ID查询是否已存在记录
		LambdaQueryWrapper<UserScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(UserScheduleEntity::getUserId, userSchedule.getUserId());
		queryWrapper.eq(UserScheduleEntity::getIsDeleted, 0);
		
		UserScheduleEntity existingSchedule = this.getOne(queryWrapper);
		
		if (existingSchedule != null) {
			// 更新现有记录
			userSchedule.setId(existingSchedule.getId());
			return this.updateById(userSchedule);
		} else {
			// 创建新记录
			return this.save(userSchedule);
		}
	}

}
