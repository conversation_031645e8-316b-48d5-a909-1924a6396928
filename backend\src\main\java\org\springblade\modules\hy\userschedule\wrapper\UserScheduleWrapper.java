/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.hy.userschedule.pojo.entity.UserScheduleEntity;
import org.springblade.modules.hy.userschedule.pojo.vo.UserScheduleVO;
import java.util.Objects;

/**
 * 用户日程信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public class UserScheduleWrapper extends BaseEntityWrapper<UserScheduleEntity, UserScheduleVO> {

	public static UserScheduleWrapper build() {
		return new UserScheduleWrapper();
	}

	@Override
	public UserScheduleVO entityVO(UserScheduleEntity userSchedule) {
		UserScheduleVO userScheduleVO = Objects.requireNonNull(BeanUtil.copyProperties(userSchedule, UserScheduleVO.class));
		return userScheduleVO;
	}

}
