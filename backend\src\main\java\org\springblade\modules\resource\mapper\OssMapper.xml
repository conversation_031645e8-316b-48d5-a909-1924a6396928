<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.resource.mapper.OssMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ossResultMap" type="org.springblade.modules.resource.pojo.entity.Oss">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="oss_code" property="ossCode"/>
        <result column="category" property="category"/>
        <result column="endpoint" property="endpoint"/>
        <result column="access_key" property="accessKey"/>
        <result column="secret_key" property="secretKey"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="app_id" property="appId"/>
        <result column="region" property="region"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectOssPage" resultMap="ossResultMap">
        select * from blade_oss where is_deleted = 0
    </select>

</mapper>
