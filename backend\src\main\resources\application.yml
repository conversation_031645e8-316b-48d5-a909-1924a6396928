#服务器配置
server:
  port: 8080
  undertow:
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true

spring:
  data:
    redis:
      ##redis 单机环境配置
      host: ${REDIS_HOST:**************}
      port: ${REDIS_PORT:32690}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:10}
      ssl:
        enabled: false
  datasource:
    url: ${SPRING_DATASOURCE_URL:***************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:gzyc}
    password: ${SPRING_DATASOURCE_PASSWORD:gzyc1234}
#    driver-class-name: com.mysql.cj.jdbc.Driver
    driver-class-name: org.postgresql.Driver
    #driver-class-name: oracle.jdbc.OracleDriver
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    #driver-class-name: dm.jdbc.driver.DmDriver
    #driver-class-name: com.yashandb.jdbc.Driver
    druid:
      # MySql、PostgreSQL、SqlServer、DaMeng校验
      validation-query: select 1
      # Oracle、YashanDB校验
      #oracle: true
      #validation-query: select 1 from dual
      validation-query-timeout: 2000
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      stat-view-servlet:
        enabled: true
        login-username: blade
        login-password: 1qaz@WSX
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: '*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*'
        session-stat-enable: true
        session-stat-max-count: 10

# mybatis
mybatis-plus:
  mapper-locations: classpath:org/springblade/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.springblade.**.entity
  #typeEnumsPackage: org.springblade.dashboard.entity.enums
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增", 1:"不操作", 2:"用户输入ID",3:"数字型snowflake", 4:"全局唯一ID UUID", 5:"字符串型snowflake";
      id-type: assign_id
      #字段策略
      insert-strategy: not_null
      update-strategy: not_null
      where-strategy: not_null
      #驼峰下划线转换
      table-underline: true
      # 逻辑删除配置
      # 逻辑删除全局值（1表示已删除，这也是Mybatis Plus的默认配置）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除，这也是Mybatis Plus的默认配置）
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'

#springdoc-openapi配置
springdoc:
  default-flat-param-object: true

#knife4j配置
knife4j:
  #基础认证
  basic:
    enable: false
    username: blade
    password: blade
  #增强配置
  setting:
    enable-swagger-models: true
    enable-document-manage: true
    enable-host: false
    enable-host-text: http://localhost
    enable-request-cache: true
    enable-filter-multipart-apis: false
    enable-filter-multipart-api-method-type: POST
    enable-footer: false
    enable-footer-custom: true
    language: zh_cn
    footer-custom-content: Copyright © 2024 BladeX All Rights Reserved

#swagger公共信息
swagger:
  title: BladeX 接口文档系统
  description: BladeX 接口文档系统
  version: 4.3.0.RELEASE
  license: Powered By BladeX
  license-url: https://license.bladex.cn
  contact:
    name: 翼宿
    email: <EMAIL>
    url: https://gitee.com/smallc

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: ${OSS_NAME:minio}
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: ${OSS_ENDPOINT:http://**********:9000}
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: ${OSS_ACCESS_KEY:OkL85reOLjtr30xaPjtV}
  #密钥key
  secret-key: ${OSS_SECRET_KEY:OR7oala7v39rHB4CC7r1zR9479FDZVSXmYlD4ftU}
  #存储桶
  bucket-name: ${OSS_BUCKET_NAME:images}

#第三方登陆配置
social:
  enabled: true
  domain: http://127.0.0.1:1888
  oauth:
    GITHUB:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/github
    GITEE:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/gitee
    WECHAT_OPEN:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/wechat
    QQ:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/qq
    DINGTALK:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/dingtalk

#flowable配置
flowable:
  activity-font-name: \u5B8B\u4F53
  label-font-name: \u5B8B\u4F53
  annotation-font-name: \u5B8B\u4F53
  check-process-definitions: false
  database-schema-update: false
  async-executor-activate: false
  async-history-executor-activate: false

#报表配置
report:
  enabled: false
  database:
    provider:
      prefix: blade-

#job服务配置
powerjob:
  worker:
    enabled: false
    app-name: blade-job
    port: 27777
    server-address: 127.0.0.1:7700

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html
  #oauth2配置
  oauth2:
    #启用 oauth2
    enabled: true
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2验签,需和前端保持一致
    public-key: 045095f9c24e89f42a0635378fe00dd397f38dbcabdaf7d4c56c38192a949f511cfcdb340d02472f8e30c6c70bc20796678494be0cb677dccf9243d42664c6c9bf
    #使用 @org.springblade.test.Sm2KeyGenerator 获取,用于国密sm2解密,前端无需配置
    private-key: 00c34f23a482c3249c16124c64f2e95ad8beb64868d95f8c2e9398f657ba38d07a
  #token配置
  token:
    #是否有状态
    state: false
    #是否单用户登录
    single: false
    #单用户登录范围
    single-level: all
    #token签名 使用 @org.springblade.test.SignKeyGenerator 获取
    sign-key: XPm4fEC3BaczvWdH7xZ22EzbU9D8suWD
    #token加密 使用 @org.springblade.test.CryptoKeyGenerator 获取
    crypto-key: 请配置cryptoKey
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用 @org.springblade.test.CryptoKeyGenerator 获取,需和前端保持一致
      aes-key: 请配置aesKey
      #使用 @org.springblade.test.CryptoKeyGenerator 获取,需和前端保持一致
      des-key: 请配置desKey
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: true
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #日志配置
  log:
    request:
      #开启控制台请求日志
      enabled: true
      #控制台请求日志忽略
      skip-url:
        - /blade-desk/notice/list
        - /blade-chat/weixin/**
      #开启错误日志入库
      error-log: true
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /blade-chat/wechat
      - /blade-chat/qq
  #安全框架配置
  secure:
    #严格模式
    #缺失令牌字段则取消授权
    strict-token: true
    #缺失请求头则取消授权
    strict-header: true
    #接口放行
    skip-url:
      - /blade-test/**
    #授权认证配置
    auth:
      - method: ALL
        pattern: /blade-chat/wechat/**
        expression: "hasAuth()"
      - method: ALL
        pattern: /blade-chat/qq/**
        expression: "hasStrictToken()"
      - method: ALL
        pattern: /blade-chat/ding/**
        expression: "hasStrictHeader()"
      - method: POST
        pattern: /blade-desk/dashboard/upload
        expression: "hasTimeAuth(9, 17)"
      - method: POST
        pattern: /blade-desk/dashboard/submit
        expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      - method: ALL
        pattern: /blade-desk/dashboard/info
        username: "blade"
        password: "blade"
    #动态签名认证配置
    sign:
      - method: ALL
        pattern: /blade-desk/dashboard/sign
        crypto: "sha1"
    #多终端认证配置
    client:
      - client-id: sword
        path-patterns:
          - /blade-sword/**
      - client-id: saber
        path-patterns:
          - /blade-saber/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: true
    #多租户授权保护
    license: false
    #动态数据库隔离功能
    dynamic-datasource: false
    #动态数据库隔离全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
      - blade_user
  #分库分表配置
  sharding:
    enabled: false

idaas:
  client: 9af0d642c1a22f1daadf50909cdc9939XxBx1ipp2Hk
  secret: WODHL4yTVZGuw56NcNYmzaq8012IkLZoZ8zyFafUZ8
  loc:
    redirect: http://************/kh360/blade-auth/idaasoauth/callback
    server: http://************
    login: http://************/slogin
  pic:
    redirect: https://10-78-65-141-a1704kt68glct3.ztna-dingtalk.com/blade-auth/idaasoauth/callback
    server: https://10-79-112-85-7zcuqumplurk.ztna-dingtalk.com
    login: https://10-78-65-141-a1704kt68glct3.ztna-dingtalk.com/slogin
