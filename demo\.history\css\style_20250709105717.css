/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 375px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    position: relative;
}

/* 头部Logo区域 */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo-left, .logo-right {
    display: flex;
    align-items: center;
    color: white;
    font-size: 14px;
    font-weight: 500;
}

.logo-left i, .logo-right i {
    font-size: 20px;
    margin-right: 8px;
}

/* 主标题区域 */
.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.main-title {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sub-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.english-title {
    font-size: 12px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 1px;
}

/* 功能模块区域 */
.function-modules {
    margin-bottom: 40px;
}

.module-row {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.module-row:first-child {
    justify-content: center;
}

.module-row:first-child .module-item {
    width: 280px;
}

.module-item {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    width: 130px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.module-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.35);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-item i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
}

.module-text {
    text-align: center;
    color: white;
}

.module-text .chinese {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.module-text .english {
    display: block;
    font-size: 10px;
    opacity: 0.8;
    letter-spacing: 0.5px;
}

/* 个人中心按钮 */
.personal-center {
    position: absolute;
    right: 30px;
    bottom: 180px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.personal-center:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

.personal-center i {
    font-size: 20px;
    color: white;
    margin-bottom: 4px;
}

.personal-center span {
    font-size: 10px;
    color: white;
    text-align: center;
}

.personal-center .english {
    font-size: 8px;
    opacity: 0.8;
}

/* 底部Logo */
.footer {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.footer-logo {
    display: flex;
    align-items: baseline;
    color: white;
}

.logo-text {
    font-size: 36px;
    font-weight: bold;
    letter-spacing: 2px;
}

.logo-number {
    font-size: 24px;
    font-weight: bold;
    margin-left: 5px;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 28px;
    }
    
    .module-item {
        width: 110px;
        height: 90px;
        padding: 15px;
    }
    
    .module-item i {
        font-size: 20px;
    }
}

@media (min-width: 376px) {
    .container {
        max-width: 400px;
    }
    
    .module-item {
        width: 140px;
        height: 110px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.module-item {
    animation: fadeInUp 0.6s ease forwards;
}

.module-row:nth-child(1) .module-item {
    animation-delay: 0.1s;
}

.module-row:nth-child(2) .module-item:nth-child(1) {
    animation-delay: 0.2s;
}

.module-row:nth-child(2) .module-item:nth-child(2) {
    animation-delay: 0.3s;
}

.module-row:nth-child(3) .module-item:nth-child(1) {
    animation-delay: 0.4s;
}

.module-row:nth-child(3) .module-item:nth-child(2) {
    animation-delay: 0.5s;
}

.module-row:nth-child(4) .module-item:nth-child(1) {
    animation-delay: 0.6s;
}

.module-row:nth-child(4) .module-item:nth-child(2) {
    animation-delay: 0.7s;
}

.personal-center {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.8s;
}
