/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 375px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    position: relative;
}

/* 顶部Logo区域 */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.logos {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo-item {
    display: flex;
    align-items: center;
    color: white;
    font-size: 14px;
    font-weight: 500;
}

.logo-item i {
    font-size: 20px;
    margin-right: 8px;
}

/* 主标题区域 */
.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.main-title {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sub-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.english-title {
    font-size: 12px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 1px;
}

/* 功能模块区域 */
.function-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 40px;
    position: relative;
}

.function-grid .function-card:first-child {
    grid-column: 1;
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
}

.function-grid .function-card:nth-child(2),
.function-grid .function-card:nth-child(3) {
    display: inline-block;
    width: calc(50% - 7.5px);
}

.function-grid .function-card:nth-child(4),
.function-grid .function-card:nth-child(5) {
    display: inline-block;
    width: calc(50% - 7.5px);
}

.function-grid .function-card:nth-child(6) {
    display: inline-block;
    width: calc(50% - 7.5px);
}

.function-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.function-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.35);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-icon i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
}

.card-text {
    text-align: center;
    color: white;
}

.card-text h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.card-text p {
    font-size: 10px;
    opacity: 0.8;
    letter-spacing: 0.5px;
    margin: 0;
}

/* 重新排列网格布局 */
@media (min-width: 320px) {
    .function-grid {
        display: block;
    }

    .function-grid .function-card:first-child {
        width: 100%;
        max-width: 280px;
        margin: 0 auto 15px auto;
        display: block;
    }

    .row-2, .row-3 {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }

    .row-2 .function-card,
    .row-3 .function-card {
        flex: 1;
        width: auto;
    }
}

/* 个人中心按钮 */
.center-button {
    position: absolute;
    right: 30px;
    bottom: 180px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.center-button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

.center-icon i {
    font-size: 20px;
    color: white;
    margin-bottom: 4px;
}

.center-button span {
    font-size: 10px;
    color: white;
    text-align: center;
    margin-bottom: 2px;
}

.center-button p {
    font-size: 8px;
    color: white;
    opacity: 0.8;
    margin: 0;
}

/* 联系我们按钮 */
.contact-button {
    position: absolute;
    left: 30px;
    bottom: 180px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.contact-button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

.contact-icon i {
    font-size: 20px;
    color: white;
    margin-bottom: 4px;
}

.contact-button span {
    font-size: 10px;
    color: white;
    text-align: center;
    margin-bottom: 2px;
}

.contact-button p {
    font-size: 8px;
    color: white;
    opacity: 0.8;
    margin: 0;
}

/* 底部Logo */
.footer {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.footer-logo {
    display: flex;
    align-items: baseline;
    color: white;
}

.logo-text {
    font-size: 36px;
    font-weight: bold;
    letter-spacing: 2px;
}

.logo-number {
    font-size: 24px;
    font-weight: bold;
    margin-left: 5px;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 28px;
    }
    
    .module-item {
        width: 110px;
        height: 90px;
        padding: 15px;
    }
    
    .module-item i {
        font-size: 20px;
    }
}

@media (min-width: 376px) {
    .container {
        max-width: 400px;
    }
    
    .module-item {
        width: 140px;
        height: 110px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.module-item {
    animation: fadeInUp 0.6s ease forwards;
}

.module-row:nth-child(1) .module-item {
    animation-delay: 0.1s;
}

.module-row:nth-child(2) .module-item:nth-child(1) {
    animation-delay: 0.2s;
}

.module-row:nth-child(2) .module-item:nth-child(2) {
    animation-delay: 0.3s;
}

.module-row:nth-child(3) .module-item:nth-child(1) {
    animation-delay: 0.4s;
}

.module-row:nth-child(3) .module-item:nth-child(2) {
    animation-delay: 0.5s;
}

.module-row:nth-child(4) .module-item:nth-child(1) {
    animation-delay: 0.6s;
}

.module-row:nth-child(4) .module-item:nth-child(2) {
    animation-delay: 0.7s;
}

.personal-center {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.8s;
}
