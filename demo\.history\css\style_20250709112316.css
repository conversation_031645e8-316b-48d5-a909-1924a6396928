/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 375px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    position: relative;
}



/* 主标题区域 */
.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    padding-top: 30px;
}

.main-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.conference-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.organizer {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 0.5px;
    margin: 0 0 8px 0;
}

.date {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 0.5px;
    margin: 0;
    font-weight: 500;
}

/* 功能模块区域 */
.function-grid {
    margin-bottom: 40px;
    position: relative;
}

/* 行布局 */
.row-1, .row-2, .row-3, .row-4 {
    margin-bottom: 15px;
}

.row-1, .row-2, .row-3 {
    display: flex;
    gap: 15px;
}

.row-4 {
    display: flex;
    justify-content: flex-start;
}

.function-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.function-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.35);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 第一行、第二行和第三行：两个卡片并排 */
.row-1 .function-card,
.row-2 .function-card,
.row-3 .function-card {
    flex: 1;
}

/* 第四行：我的住宿 - 左对齐 */
.row-4 .function-card {
    width: calc(50% - 7.5px);
}

.card-icon i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
}

.card-text {
    text-align: center;
    color: white;
}

.card-text h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.card-text p {
    font-size: 10px;
    opacity: 0.8;
    letter-spacing: 0.5px;
    margin: 0;
}

/* 个人中心按钮 */
.center-button {
    position: absolute;
    right: 30px;
    bottom: 180px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.center-button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

.center-icon i {
    font-size: 20px;
    color: white;
    margin-bottom: 4px;
}

.center-button span {
    font-size: 10px;
    color: white;
    text-align: center;
    margin-bottom: 2px;
}

.center-button p {
    font-size: 8px;
    color: white;
    opacity: 0.8;
    margin: 0;
}

/* 联系我们按钮 */
.contact-button {
    position: absolute;
    left: 30px;
    bottom: 180px;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.contact-button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.4);
}

.contact-icon i {
    font-size: 20px;
    color: white;
    margin-bottom: 4px;
}

.contact-button span {
    font-size: 10px;
    color: white;
    text-align: center;
    margin-bottom: 2px;
}

.contact-button p {
    font-size: 8px;
    color: white;
    opacity: 0.8;
    margin: 0;
}

/* 底部Logo */
.footer {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
}

.logo-text {
    font-size: 36px;
    font-weight: bold;
    letter-spacing: 2px;
    margin-bottom: 5px;
}

.logo-sub {
    font-size: 12px;
    font-weight: normal;
    opacity: 0.8;
    letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 28px;
    }
    
    .module-item {
        width: 110px;
        height: 90px;
        padding: 15px;
    }
    
    .module-item i {
        font-size: 20px;
    }
}

@media (min-width: 376px) {
    .container {
        max-width: 400px;
    }
    
    .module-item {
        width: 140px;
        height: 110px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.module-item {
    animation: fadeInUp 0.6s ease forwards;
}

.module-row:nth-child(1) .module-item {
    animation-delay: 0.1s;
}

.module-row:nth-child(2) .module-item:nth-child(1) {
    animation-delay: 0.2s;
}

.module-row:nth-child(2) .module-item:nth-child(2) {
    animation-delay: 0.3s;
}

.module-row:nth-child(3) .module-item:nth-child(1) {
    animation-delay: 0.4s;
}

.module-row:nth-child(3) .module-item:nth-child(2) {
    animation-delay: 0.5s;
}

.module-row:nth-child(4) .module-item:nth-child(1) {
    animation-delay: 0.6s;
}

.module-row:nth-child(4) .module-item:nth-child(2) {
    animation-delay: 0.7s;
}

.personal-center {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.8s;
}
