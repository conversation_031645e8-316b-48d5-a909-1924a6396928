/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 375px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    position: relative;
}



/* 主标题区域 */
.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    padding-top: 30px;
}

.main-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.conference-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.organizer {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 0.5px;
    margin: 0 0 8px 0;
}

.date {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
    letter-spacing: 0.5px;
    margin: 0;
    font-weight: 500;
}

/* 功能模块区域 */
.function-grid {
    margin-bottom: 40px;
    position: relative;
}

/* 行布局 */
.row-1, .row-2, .row-3, .row-4 {
    margin-bottom: 15px;
    display: flex;
    gap: 15px;
}

.function-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* 科技感光效背景 */
.function-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.function-card:hover::before {
    animation: shimmer 0.6s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

.function-card:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.4);
    box-shadow: 0 15px 35px rgba(70, 130, 180, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* 图标动效 */
.card-icon {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

.function-card:hover .card-icon {
    transform: scale(1.2) rotateY(360deg);
}

.card-icon i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.function-card:hover .card-icon i {
    color: #87CEEB;
    text-shadow: 0 0 20px rgba(135, 206, 235, 0.8);
}

/* 文字动效 */
.card-text {
    text-align: center;
    color: white;
    transition: all 0.3s ease;
    position: relative;
}

.function-card:hover .card-text {
    transform: translateY(-2px);
}

.card-text h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
    transition: all 0.3s ease;
}

.function-card:hover .card-text h3 {
    color: #87CEEB;
    text-shadow: 0 0 10px rgba(135, 206, 235, 0.6);
}

.card-text p {
    font-size: 10px;
    opacity: 0.8;
    letter-spacing: 0.5px;
    margin: 0;
    transition: all 0.3s ease;
}

.function-card:hover .card-text p {
    opacity: 1;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.9);
}

/* 所有行：两个卡片并排 */
.row-1 .function-card,
.row-2 .function-card,
.row-3 .function-card,
.row-4 .function-card {
    flex: 1;
}



/* 联系我们气泡按钮 */
.contact-bubble {
    position: fixed;
    right: 20px;
    bottom: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
    z-index: 1000;
}

.contact-bubble:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.4);
}

.contact-bubble i {
    font-size: 24px;
    color: white;
}

/* 气泡动画效果 */
.contact-bubble::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(70, 130, 180, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}



/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 28px;
    }
    
    .module-item {
        width: 110px;
        height: 90px;
        padding: 15px;
    }
    
    .module-item i {
        font-size: 20px;
    }
}

@media (min-width: 376px) {
    .container {
        max-width: 400px;
    }
    
    .module-item {
        width: 140px;
        height: 110px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.module-item {
    animation: fadeInUp 0.6s ease forwards;
}

.module-row:nth-child(1) .module-item {
    animation-delay: 0.1s;
}

.module-row:nth-child(2) .module-item:nth-child(1) {
    animation-delay: 0.2s;
}

.module-row:nth-child(2) .module-item:nth-child(2) {
    animation-delay: 0.3s;
}

.module-row:nth-child(3) .module-item:nth-child(1) {
    animation-delay: 0.4s;
}

.module-row:nth-child(3) .module-item:nth-child(2) {
    animation-delay: 0.5s;
}

.module-row:nth-child(4) .module-item:nth-child(1) {
    animation-delay: 0.6s;
}

.module-row:nth-child(4) .module-item:nth-child(2) {
    animation-delay: 0.7s;
}
