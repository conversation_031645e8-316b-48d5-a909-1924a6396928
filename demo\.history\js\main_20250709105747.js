// 页面导航函数
function navigateTo(page) {
    // 添加点击动画效果
    const clickedElement = event.currentTarget;
    clickedElement.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        clickedElement.style.transform = '';
    }, 150);

    // 模拟页面跳转（在实际项目中可以使用路由）
    setTimeout(() => {
        switch(page) {
            case 'register':
                showPage('会议报名', '这里是会议报名页面');
                break;
            case 'checkin':
                showPage('现场签到', '这里是现场签到页面');
                break;
            case 'agenda':
                showPage('大会议程', '这里是大会议程页面');
                break;
            case 'photo':
                showPage('在线相册', '这里是在线相册页面');
                break;
            case 'guide':
                showPage('参会指南', '这里是参会指南页面');
                break;
            case 'meal':
                showPage('我的餐券', '这里是我的餐券页面');
                break;
            case 'profile':
                showPage('个人中心', '这里是个人中心页面');
                break;
            case 'contact':
                showPage('联系我们', '这里是联系我们页面');
                break;
            default:
                console.log('未知页面:', page);
        }
    }, 200);
}

// 显示页面内容（模拟页面跳转）
function showPage(title, content) {
    // 创建模态框显示页面内容
    const modal = document.createElement('div');
    modal.className = 'page-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>${content}</p>
                <div class="demo-content">
                    <p>这是一个静态演示页面</p>
                    <p>在实际项目中，这里会显示具体的功能内容</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 添加模态框样式
    if (!document.getElementById('modal-styles')) {
        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .page-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 15px;
                width: 90%;
                max-width: 400px;
                max-height: 80%;
                overflow: hidden;
                animation: slideUp 0.3s ease;
            }
            
            .modal-header {
                background: linear-gradient(135deg, #4682B4, #1E90FF);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-header h2 {
                margin: 0;
                font-size: 18px;
            }
            
            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.3s ease;
            }
            
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .modal-body {
                padding: 30px 20px;
                text-align: center;
            }
            
            .demo-content {
                margin-top: 20px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
                border-left: 4px solid #4682B4;
            }
            
            .demo-content p {
                margin: 10px 0;
                color: #666;
                font-size: 14px;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideUp {
                from { 
                    opacity: 0;
                    transform: translateY(50px);
                }
                to { 
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.page-modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 添加淡出动画
const fadeOutStyle = document.createElement('style');
fadeOutStyle.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
`;
document.head.appendChild(fadeOutStyle);

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面加载动画
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.6s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
    
    // 添加触摸反馈
    const moduleItems = document.querySelectorAll('.module-item');
    const personalCenter = document.querySelector('.personal-center');
    
    [...moduleItems, personalCenter].forEach(item => {
        item.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    console.log('知慧会务系统已加载完成');
});

// 防止页面缩放
document.addEventListener('touchstart', function(event) {
    if (event.touches.length > 1) {
        event.preventDefault();
    }
});

let lastTouchEnd = 0;
document.addEventListener('touchend', function(event) {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);
