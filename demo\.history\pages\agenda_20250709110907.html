<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大会议程 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>大会议程</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-calendar-alt"></i>
                    <h2>会议议程</h2>
                    <p>数智攀登，管理跃升 - 企业管理现场会详细安排</p>
                </div>

                <!-- 第一天 -->
                <div class="day-section">
                    <h3 class="day-title">
                        <i class="fas fa-calendar-day"></i>
                        第一天 - 2025年9月15日
                    </h3>
                    
                    <div class="list-item">
                        <div class="time">08:30 - 09:00</div>
                        <h3>签到注册</h3>
                        <p>参会者签到，领取会议资料</p>
                        <p><i class="fas fa-map-marker-alt"></i> 主会场大厅</p>
                    </div>

                    <div class="list-item">
                        <div class="time">09:00 - 09:30</div>
                        <h3>开幕式</h3>
                        <p>大会开幕致辞，嘉宾介绍</p>
                        <p><i class="fas fa-user"></i> 主办方领导</p>
                        <p><i class="fas fa-map-marker-alt"></i> 主会场</p>
                    </div>

                    <div class="list-item">
                        <div class="time">09:30 - 10:30</div>
                        <h3>主题演讲：数智攀登，管理跃升</h3>
                        <p>探讨数字化智能化在企业管理中的创新应用</p>
                        <p><i class="fas fa-user"></i> 公司领导</p>
                        <p><i class="fas fa-map-marker-alt"></i> 主会场</p>
                    </div>

                    <div class="list-item">
                        <div class="time">10:30 - 10:45</div>
                        <h3>茶歇</h3>
                        <p>休息时间，自由交流</p>
                        <p><i class="fas fa-map-marker-alt"></i> 休息区</p>
                    </div>

                    <div class="list-item">
                        <div class="time">10:45 - 12:00</div>
                        <h3>圆桌讨论：数字化转型</h3>
                        <p>行业专家共同探讨会务行业数字化转型</p>
                        <p><i class="fas fa-users"></i> 多位行业专家</p>
                        <p><i class="fas fa-map-marker-alt"></i> 主会场</p>
                    </div>

                    <div class="list-item">
                        <div class="time">12:00 - 13:30</div>
                        <h3>午餐时间</h3>
                        <p>自助午餐，网络交流</p>
                        <p><i class="fas fa-map-marker-alt"></i> 餐厅</p>
                    </div>
                </div>

                <!-- 第二天 -->
                <div class="day-section">
                    <h3 class="day-title">
                        <i class="fas fa-calendar-day"></i>
                        第二天 - 2025年3月16日
                    </h3>
                    
                    <div class="list-item">
                        <div class="time">09:00 - 10:00</div>
                        <h3>技术分享：AI在会务中的实践</h3>
                        <p>分享人工智能技术在实际会务场景中的应用案例</p>
                        <p><i class="fas fa-user"></i> 李工程师 - 知慧科技</p>
                        <p><i class="fas fa-map-marker-alt"></i> 技术分会场</p>
                    </div>

                    <div class="list-item">
                        <div class="time">10:00 - 11:00</div>
                        <h3>产品演示</h3>
                        <p>知慧会务系统功能演示与体验</p>
                        <p><i class="fas fa-user"></i> 产品团队</p>
                        <p><i class="fas fa-map-marker-alt"></i> 演示区</p>
                    </div>

                    <div class="list-item">
                        <div class="time">11:00 - 12:00</div>
                        <h3>闭幕式</h3>
                        <p>大会总结，颁奖典礼</p>
                        <p><i class="fas fa-user"></i> 组委会</p>
                        <p><i class="fas fa-map-marker-alt"></i> 主会场</p>
                    </div>
                </div>

                <!-- 下载议程 -->
                <div class="download-section">
                    <button class="submit-btn" onclick="downloadAgenda()">
                        <i class="fas fa-download"></i>
                        下载完整议程
                    </button>
                </div>
            </div>
        </main>
    </div>

    <style>
        .day-section {
            margin-bottom: 30px;
        }

        .day-title {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .list-item .time {
            background: #f0f8ff;
            color: #4682B4;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }

        .download-section {
            margin-top: 30px;
            text-align: center;
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function downloadAgenda() {
            // 模拟下载功能
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备下载...';
            btn.disabled = true;

            setTimeout(() => {
                alert('议程文件已准备好！\n（这是一个演示功能）');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }
    </script>
</body>
</html>
