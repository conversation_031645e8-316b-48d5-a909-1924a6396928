<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会务助手 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>会务助手</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="chat-container">
                <!-- AI助手介绍 -->
                <div class="ai-intro">
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-info">
                        <h3>智能会务助手</h3>
                        <p>我是您的专属会务助手，可以为您解答关于会议的各种问题</p>
                    </div>
                </div>

                <!-- 快速问题 -->
                <div class="quick-questions">
                    <h4>常见问题</h4>
                    <div class="question-grid">
                        <button class="quick-btn" onclick="askQuestion('会议时间安排')">
                            <i class="fas fa-clock"></i>
                            会议时间安排
                        </button>
                        <button class="quick-btn" onclick="askQuestion('会议地点在哪')">
                            <i class="fas fa-map-marker-alt"></i>
                            会议地点
                        </button>
                        <button class="quick-btn" onclick="askQuestion('如何签到')">
                            <i class="fas fa-check-circle"></i>
                            如何签到
                        </button>
                        <button class="quick-btn" onclick="askQuestion('用餐安排')">
                            <i class="fas fa-utensils"></i>
                            用餐安排
                        </button>
                        <button class="quick-btn" onclick="askQuestion('住宿信息')">
                            <i class="fas fa-bed"></i>
                            住宿信息
                        </button>
                        <button class="quick-btn" onclick="askQuestion('联系方式')">
                            <i class="fas fa-phone"></i>
                            联系方式
                        </button>
                    </div>
                </div>

                <!-- 聊天区域 -->
                <div class="chat-area" id="chatArea">
                    <div class="welcome-message">
                        <div class="message ai-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>您好！我是智能会务助手，很高兴为您服务。您可以询问关于"数智攀登，管理跃升"企业管理现场会的任何问题，我会尽力为您解答。</p>
                                <span class="message-time">刚刚</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-area">
                    <div class="input-container">
                        <input type="text" id="messageInput" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
                        <button class="send-btn" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .page-content {
            padding: 0;
            height: calc(100vh - 80px);
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.95);
            margin: 20px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .ai-intro {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .ai-avatar {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ai-avatar i {
            font-size: 24px;
            color: white;
        }

        .ai-info h3 {
            margin-bottom: 5px;
            font-size: 16px;
        }

        .ai-info p {
            font-size: 12px;
            opacity: 0.9;
            margin: 0;
        }

        .quick-questions {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .quick-questions h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 15px;
            text-align: center;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .quick-btn {
            background: rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.3);
            color: #4682B4;
            padding: 12px 8px;
            border-radius: 10px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .quick-btn:hover {
            background: #4682B4;
            color: white;
        }

        .quick-btn i {
            font-size: 16px;
        }

        .chat-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
            gap: 10px;
        }

        .ai-message {
            justify-content: flex-start;
        }

        .user-message {
            justify-content: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .ai-message .message-avatar {
            background: #4682B4;
            color: white;
        }

        .user-message .message-avatar {
            background: #28a745;
            color: white;
        }

        .message-content {
            max-width: 70%;
            background: white;
            border-radius: 15px;
            padding: 12px 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user-message .message-content {
            background: #4682B4;
            color: white;
        }

        .message-content p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 10px;
            opacity: 0.7;
            display: block;
            margin-top: 5px;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-container input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-container input:focus {
            border-color: #4682B4;
        }

        .send-btn {
            width: 45px;
            height: 45px;
            background: #4682B4;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            background: #1E90FF;
            transform: scale(1.1);
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .typing-dots {
            display: flex;
            gap: 3px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #4682B4;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
    </style>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');

        // 预设回答
        const responses = {
            '会议时间安排': '会议时间为2025年9月15日-16日，共两天。第一天08:30开始签到，09:00正式开始。详细议程请查看"议程"页面。',
            '会议地点在哪': '会议地点在广东烟草大厦会议中心，地址：广州市天河区珠江新城。可乘坐地铁3号线/5号线到珠江新城站。',
            '如何签到': '您可以通过扫码签到或手动签到两种方式。建议提前15分钟到达会场，在签到页面完成签到流程。',
            '用餐安排': '会议期间提供工作餐，午餐时间为12:00-13:30。如有特殊饮食要求，请提前联系会务组。',
            '住宿信息': '推荐住宿：广州大酒店（步行3分钟）、珠江宾馆（步行8分钟）。详细信息请查看"我的住宿"页面。',
            '联系方式': '会务组联系人：张先生 138-0000-0000，技术支持：李女士 139-0000-0000。如有紧急情况请及时联系。'
        };

        function goBack() {
            window.history.back();
        }

        function askQuestion(question) {
            sendMessage(question);
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage(predefinedMessage = null) {
            const message = predefinedMessage || messageInput.value.trim();
            if (!message) return;

            // 添加用户消息
            addMessage(message, 'user');
            
            // 清空输入框
            if (!predefinedMessage) {
                messageInput.value = '';
            }

            // 显示打字指示器
            showTypingIndicator();

            // 模拟AI回复
            setTimeout(() => {
                hideTypingIndicator();
                const response = getAIResponse(message);
                addMessage(response, 'ai');
            }, 1500);
        }

        function addMessage(content, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const avatar = sender === 'ai' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    ${avatar}
                </div>
                <div class="message-content">
                    <p>${content}</p>
                    <span class="message-time">${time}</span>
                </div>
            `;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typingIndicator';
            typingDiv.innerHTML = `
                <div class="message-avatar" style="background: #4682B4; color: white;">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            
            chatArea.appendChild(typingDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function getAIResponse(message) {
            // 检查是否有预设回答
            for (const [key, value] of Object.entries(responses)) {
                if (message.includes(key) || message.includes(key.replace(/\s/g, ''))) {
                    return value;
                }
            }

            // 关键词匹配
            if (message.includes('时间') || message.includes('几点') || message.includes('什么时候')) {
                return responses['会议时间安排'];
            }
            
            if (message.includes('地点') || message.includes('地址') || message.includes('在哪')) {
                return responses['会议地点在哪'];
            }
            
            if (message.includes('签到') || message.includes('报到')) {
                return responses['如何签到'];
            }
            
            if (message.includes('吃饭') || message.includes('用餐') || message.includes('午餐')) {
                return responses['用餐安排'];
            }
            
            if (message.includes('住宿') || message.includes('酒店') || message.includes('宾馆')) {
                return responses['住宿信息'];
            }
            
            if (message.includes('联系') || message.includes('电话') || message.includes('咨询')) {
                return responses['联系方式'];
            }

            // 默认回答
            return '感谢您的提问！关于这个问题，建议您查看参会指南或联系会务组获取更详细的信息。您也可以点击上方的快速问题按钮获取常见问题的答案。';
        }

        // 页面加载时聚焦输入框
        document.addEventListener('DOMContentLoaded', function() {
            messageInput.focus();
        });
    </script>
</body>
</html>
