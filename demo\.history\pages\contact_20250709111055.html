<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>联系我们</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-phone"></i>
                    <h2>联系方式</h2>
                    <p>如有任何问题，请随时联系我们</p>
                </div>

                <!-- 联系方式卡片 -->
                <div class="contact-methods">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-info">
                            <h3>客服热线</h3>
                            <p>************</p>
                            <small>工作时间：9:00-18:00</small>
                        </div>
                        <button class="contact-action" onclick="callPhone('************')">
                            <i class="fas fa-phone"></i>
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-info">
                            <h3>邮箱咨询</h3>
                            <p><EMAIL></p>
                            <small>24小时内回复</small>
                        </div>
                        <button class="contact-action" onclick="sendEmail('<EMAIL>')">
                            <i class="fas fa-envelope"></i>
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div class="contact-info">
                            <h3>微信客服</h3>
                            <p>zhihui_service</p>
                            <small>扫码添加客服微信</small>
                        </div>
                        <button class="contact-action" onclick="showQRCode()">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-info">
                            <h3>公司地址</h3>
                            <p>北京市朝阳区科技园区</p>
                            <small>知慧科技大厦18层</small>
                        </div>
                        <button class="contact-action" onclick="showMap()">
                            <i class="fas fa-map"></i>
                        </button>
                    </div>
                </div>

                <!-- 快速反馈 -->
                <div class="feedback-section">
                    <h3>快速反馈</h3>
                    <form class="feedback-form">
                        <div class="form-group">
                            <label for="feedback-type">反馈类型</label>
                            <select id="feedback-type" name="type">
                                <option value="">请选择反馈类型</option>
                                <option value="bug">功能问题</option>
                                <option value="suggestion">建议意见</option>
                                <option value="complaint">投诉建议</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="feedback-content">反馈内容</label>
                            <textarea id="feedback-content" name="content" rows="4" placeholder="请详细描述您的问题或建议..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="feedback-contact">联系方式（可选）</label>
                            <input type="text" id="feedback-contact" name="contact" placeholder="手机号或邮箱">
                        </div>

                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane"></i>
                            提交反馈
                        </button>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <style>
        .contact-methods {
            margin: 30px 0;
        }

        .contact-item {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .contact-icon i {
            color: white;
            font-size: 20px;
        }

        .contact-info {
            flex: 1;
        }

        .contact-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .contact-info p {
            color: #4682B4;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .contact-info small {
            color: #666;
            font-size: 12px;
        }

        .contact-action {
            width: 40px;
            height: 40px;
            background: rgba(70, 130, 180, 0.1);
            border: none;
            border-radius: 50%;
            color: #4682B4;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .contact-action:hover {
            background: #4682B4;
            color: white;
        }

        .feedback-section {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .feedback-section h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
        }

        .feedback-form textarea {
            resize: vertical;
            min-height: 100px;
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function callPhone(phone) {
            window.location.href = `tel:${phone}`;
        }

        function sendEmail(email) {
            window.location.href = `mailto:${email}`;
        }

        function showQRCode() {
            alert('微信二维码\n（演示功能）\n请扫描二维码添加客服微信');
        }

        function showMap() {
            alert('地图导航\n（演示功能）\n将打开地图应用导航到公司地址');
        }

        // 反馈表单提交
        document.querySelector('.feedback-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const type = document.getElementById('feedback-type').value;
            const content = document.getElementById('feedback-content').value;

            if (!type || !content.trim()) {
                alert('请选择反馈类型并填写反馈内容');
                return;
            }

            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
            submitBtn.disabled = true;

            setTimeout(() => {
                alert('反馈提交成功！\n我们会尽快处理您的反馈。');
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> 提交反馈';
                submitBtn.disabled = false;
                this.reset();
            }, 2000);
        });
    </script>
</body>
</html>
