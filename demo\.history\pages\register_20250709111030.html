<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议报名 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>会议报名</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="form-container">
                <div class="form-header">
                    <i class="fas fa-user-plus"></i>
                    <h2>参会报名</h2>
                    <p>请填写以下信息完成报名</p>
                </div>

                <form class="register-form">
                    <div class="form-group">
                        <label for="name">姓名 *</label>
                        <input type="text" id="name" name="name" required placeholder="请输入您的姓名">
                    </div>

                    <div class="form-group">
                        <label for="phone">手机号 *</label>
                        <input type="tel" id="phone" name="phone" required placeholder="请输入手机号">
                    </div>

                    <div class="form-group">
                        <label for="email">邮箱</label>
                        <input type="email" id="email" name="email" placeholder="请输入邮箱地址">
                    </div>

                    <div class="form-group">
                        <label for="company">公司/机构</label>
                        <input type="text" id="company" name="company" placeholder="请输入公司或机构名称">
                    </div>

                    <div class="form-group">
                        <label for="position">职位</label>
                        <input type="text" id="position" name="position" placeholder="请输入您的职位">
                    </div>

                    <div class="form-group">
                        <label for="category">参会类型 *</label>
                        <select id="category" name="category" required>
                            <option value="">请选择参会类型</option>
                            <option value="leadership">公司领导</option>
                            <option value="manager">部门负责人</option>
                            <option value="staff">管理人员</option>
                            <option value="guest">特邀嘉宾</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="dietary">饮食要求</label>
                        <select id="dietary" name="dietary">
                            <option value="normal">无特殊要求</option>
                            <option value="vegetarian">素食</option>
                            <option value="halal">清真</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="agreement" name="agreement" required>
                            <span class="checkmark"></span>
                            我已阅读并同意<a href="#" class="link">《参会协议》</a>
                        </label>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-check"></i>
                        提交报名
                    </button>
                </form>
            </div>
        </main>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // 表单提交处理
        document.querySelector('.register-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 简单的表单验证
            const name = document.getElementById('name').value;
            const phone = document.getElementById('phone').value;
            const category = document.getElementById('category').value;
            const agreement = document.getElementById('agreement').checked;

            if (!name || !phone || !category || !agreement) {
                alert('请填写必填项并同意参会协议');
                return;
            }

            // 模拟提交成功
            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
            submitBtn.disabled = true;

            setTimeout(() => {
                alert('报名成功！我们会尽快与您联系。');
                submitBtn.innerHTML = '<i class="fas fa-check"></i> 提交报名';
                submitBtn.disabled = false;
                this.reset();
            }, 2000);
        });
    </script>
</body>
</html>
