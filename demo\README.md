# 知慧会务系统 H5 Demo

这是一个基于HTML5的静态会务管理系统演示项目，采用现代化的UI设计和响应式布局。

## 项目结构

```
知慧会务系统/
├── index.html              # 主页面
├── css/
│   ├── style.css           # 主样式文件
│   └── pages.css           # 页面专用样式
├── js/
│   └── main.js             # 主要JavaScript文件
├── pages/                  # 功能页面
│   ├── register.html       # 会议报名
│   ├── agenda.html         # 大会议程
│   ├── profile.html        # 个人中心
│   └── contact.html        # 联系我们
└── README.md               # 项目说明
```

## 功能特性

### 主要功能模块
- **会议报名**: 在线报名表单，支持多种参会类型
- **现场签到**: 签到功能（演示）
- **大会议程**: 详细的会议日程安排
- **在线相册**: 会议照片展示（演示）
- **参会指南**: 参会相关指导信息（演示）
- **我的餐券**: 餐券管理功能（演示）
- **个人中心**: 用户信息和统计数据
- **联系我们**: 多种联系方式和反馈表单

### 设计特点
- **蓝色渐变背景**: 采用现代化的渐变色设计
- **毛玻璃效果**: 使用backdrop-filter实现半透明毛玻璃效果
- **响应式布局**: 适配不同屏幕尺寸的移动设备
- **动画效果**: 流畅的过渡动画和交互反馈
- **卡片式设计**: 清晰的信息层次和视觉分组

### 技术栈
- **HTML5**: 语义化标签和现代HTML特性
- **CSS3**: Flexbox布局、Grid布局、渐变、动画
- **JavaScript**: ES6+语法、DOM操作、事件处理
- **Font Awesome**: 图标库
- **响应式设计**: 移动优先的设计理念

## 使用说明

### 本地运行
1. 直接在浏览器中打开 `index.html` 文件
2. 或使用本地服务器（推荐）：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   
   # 使用PHP
   php -S localhost:8000
   ```

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 页面说明

### 主页面 (index.html)
- 顶部Logo区域
- 主标题和副标题
- 6个功能模块卡片
- 个人中心和联系我们按钮
- 底部品牌Logo

### 会议报名 (pages/register.html)
- 完整的报名表单
- 表单验证
- 提交反馈

### 大会议程 (pages/agenda.html)
- 分天显示议程
- 时间、地点、演讲者信息
- 议程下载功能

### 个人中心 (pages/profile.html)
- 用户信息展示
- 功能菜单
- 参会统计数据

### 联系我们 (pages/contact.html)
- 多种联系方式
- 快速反馈表单
- 交互式联系操作

## 自定义说明

### 修改主题色
在 `css/style.css` 中修改以下变量：
```css
/* 主要渐变色 */
background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);

/* 主题色 */
color: #4682B4;
```

### 添加新页面
1. 在 `pages/` 目录下创建新的HTML文件
2. 引入必要的CSS文件
3. 在 `js/main.js` 中添加导航逻辑

### 修改布局
- 主页面布局：修改 `css/style.css` 中的 `.function-grid` 相关样式
- 页面布局：修改 `css/pages.css` 中的相关样式

## 注意事项

1. **演示功能**: 部分功能为演示性质，实际项目中需要连接后端API
2. **图片资源**: 当前使用Font Awesome图标，可根据需要替换为自定义图片
3. **数据存储**: 表单数据仅在前端处理，实际项目需要后端支持
4. **安全性**: 生产环境需要添加适当的安全措施

## 开发建议

### 后续开发
- 集成后端API
- 添加用户认证系统
- 实现数据持久化
- 添加推送通知
- 优化性能和SEO

### 部署建议
- 使用CDN加速静态资源
- 启用Gzip压缩
- 配置适当的缓存策略
- 添加HTTPS支持

## 联系信息

如有问题或建议，请联系开发团队。

---

© 2025 知慧科技有限公司. 保留所有权利。
