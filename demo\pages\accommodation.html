<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的住宿 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>我的住宿</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-bed"></i>
                    <h2>住宿信息</h2>
                    <p>会议期间的酒店住宿安排</p>
                </div>

                <!-- 住宿状态 -->
                <div class="accommodation-status">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="status-info">
                            <h3>住宿已确认</h3>
                            <p>您的住宿安排已确认，请查看详细信息</p>
                        </div>
                        <div class="status-badge">
                            已预订
                        </div>
                    </div>
                </div>

                <!-- 酒店信息 -->
                <div class="hotel-info">
                    <div class="hotel-card">
                        <div class="hotel-header">
                            <div class="hotel-name">
                                <h3>广州大酒店</h3>
                                <div class="hotel-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>5星级</span>
                                </div>
                            </div>
                            <div class="hotel-image">
                                <i class="fas fa-building"></i>
                            </div>
                        </div>
                        
                        <div class="hotel-details">
                            <div class="detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div>
                                    <strong>酒店地址</strong>
                                    <p>广州市天河区珠江新城花城大道</p>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <i class="fas fa-walking"></i>
                                <div>
                                    <strong>距离会场</strong>
                                    <p>步行3分钟到达会议中心</p>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <i class="fas fa-phone"></i>
                                <div>
                                    <strong>联系电话</strong>
                                    <p>020-87654321</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 房间信息 -->
                <div class="room-info">
                    <h3>房间详情</h3>
                    <div class="room-card">
                        <div class="room-header">
                            <div class="room-type">
                                <i class="fas fa-bed"></i>
                                <span>商务标准间</span>
                            </div>
                            <div class="room-number">
                                房间号：<strong>1208</strong>
                            </div>
                        </div>
                        
                        <div class="room-details">
                            <div class="room-feature">
                                <i class="fas fa-wifi"></i>
                                <span>免费WiFi</span>
                            </div>
                            <div class="room-feature">
                                <i class="fas fa-tv"></i>
                                <span>液晶电视</span>
                            </div>
                            <div class="room-feature">
                                <i class="fas fa-snowflake"></i>
                                <span>空调</span>
                            </div>
                            <div class="room-feature">
                                <i class="fas fa-bath"></i>
                                <span>独立卫浴</span>
                            </div>
                        </div>
                        
                        <div class="checkin-info">
                            <div class="checkin-item">
                                <strong>入住时间：</strong>
                                <span>2025年9月14日 14:00</span>
                            </div>
                            <div class="checkin-item">
                                <strong>退房时间：</strong>
                                <span>2025年9月17日 12:00</span>
                            </div>
                            <div class="checkin-item">
                                <strong>住宿天数：</strong>
                                <span>3晚</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务信息 -->
                <div class="hotel-services">
                    <h3>酒店服务</h3>
                    <div class="services-grid">
                        <div class="service-item">
                            <i class="fas fa-concierge-bell"></i>
                            <span>24小时前台</span>
                        </div>
                        <div class="service-item">
                            <i class="fas fa-utensils"></i>
                            <span>餐厅服务</span>
                        </div>
                        <div class="service-item">
                            <i class="fas fa-dumbbell"></i>
                            <span>健身中心</span>
                        </div>
                        <div class="service-item">
                            <i class="fas fa-swimming-pool"></i>
                            <span>游泳池</span>
                        </div>
                        <div class="service-item">
                            <i class="fas fa-car"></i>
                            <span>停车场</span>
                        </div>
                        <div class="service-item">
                            <i class="fas fa-luggage-cart"></i>
                            <span>行李服务</span>
                        </div>
                    </div>
                </div>

                <!-- 交通指南 -->
                <div class="transportation">
                    <h3>交通指南</h3>
                    <div class="transport-list">
                        <div class="transport-item">
                            <div class="transport-icon">
                                <i class="fas fa-plane"></i>
                            </div>
                            <div class="transport-info">
                                <strong>从机场</strong>
                                <p>白云机场 → 地铁3号线 → 珠江新城站</p>
                                <small>约45分钟</small>
                            </div>
                        </div>
                        
                        <div class="transport-item">
                            <div class="transport-icon">
                                <i class="fas fa-train"></i>
                            </div>
                            <div class="transport-info">
                                <strong>从火车站</strong>
                                <p>广州南站 → 地铁2号线转3号线 → 珠江新城站</p>
                                <small>约30分钟</small>
                            </div>
                        </div>
                        
                        <div class="transport-item">
                            <div class="transport-icon">
                                <i class="fas fa-subway"></i>
                            </div>
                            <div class="transport-info">
                                <strong>地铁</strong>
                                <p>3号线/5号线珠江新城站A出口</p>
                                <small>步行2分钟</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-btn primary" onclick="viewMap()">
                        <i class="fas fa-map"></i>
                        查看地图
                    </button>
                    <button class="action-btn secondary" onclick="callHotel()">
                        <i class="fas fa-phone"></i>
                        联系酒店
                    </button>
                    <button class="action-btn secondary" onclick="modifyBooking()">
                        <i class="fas fa-edit"></i>
                        修改预订
                    </button>
                </div>

                <!-- 紧急联系 -->
                <div class="emergency-contact">
                    <h3>紧急联系</h3>
                    <div class="contact-list">
                        <div class="contact-item">
                            <strong>会务组：</strong>
                            <span>张先生 138-0000-0000</span>
                        </div>
                        <div class="contact-item">
                            <strong>酒店前台：</strong>
                            <span>020-87654321</span>
                        </div>
                        <div class="contact-item">
                            <strong>紧急救援：</strong>
                            <span>120 / 110</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .accommodation-status {
            margin: 20px 0;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #28a745;
        }

        .status-icon {
            width: 50px;
            height: 50px;
            background: #28a745;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-icon i {
            color: white;
            font-size: 24px;
        }

        .status-info {
            flex: 1;
        }

        .status-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .status-info p {
            color: #666;
            font-size: 13px;
            margin: 0;
        }

        .status-badge {
            background: #d4edda;
            color: #155724;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .hotel-info, .room-info, .hotel-services, .transportation, .emergency-contact {
            margin: 30px 0;
        }

        .hotel-info h3, .room-info h3, .hotel-services h3, .transportation h3, .emergency-contact h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .hotel-card, .room-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .hotel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .hotel-name h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 8px;
        }

        .hotel-rating {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .hotel-rating i {
            color: #ffc107;
            font-size: 12px;
        }

        .hotel-rating span {
            color: #666;
            font-size: 12px;
            margin-left: 5px;
        }

        .hotel-image {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hotel-image i {
            color: white;
            font-size: 24px;
        }

        .hotel-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .detail-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .detail-item i {
            color: #4682B4;
            font-size: 16px;
            margin-top: 2px;
        }

        .detail-item strong {
            color: #333;
            font-size: 14px;
            display: block;
            margin-bottom: 3px;
        }

        .detail-item p {
            color: #666;
            font-size: 13px;
            margin: 0;
        }

        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .room-type {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .room-type i {
            color: #4682B4;
            font-size: 18px;
        }

        .room-type span {
            color: #333;
            font-size: 16px;
            font-weight: 500;
        }

        .room-number {
            color: #4682B4;
            font-size: 14px;
        }

        .room-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .room-feature {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .room-feature i {
            color: #4682B4;
            font-size: 14px;
        }

        .room-feature span {
            color: #333;
            font-size: 12px;
        }

        .checkin-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .checkin-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .checkin-item:last-child {
            margin-bottom: 0;
        }

        .checkin-item strong {
            color: #333;
            font-size: 13px;
        }

        .checkin-item span {
            color: #4682B4;
            font-size: 13px;
            font-weight: 500;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .service-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 15px 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .service-item i {
            color: #4682B4;
            font-size: 20px;
        }

        .service-item span {
            color: #333;
            font-size: 11px;
            text-align: center;
        }

        .transport-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .transport-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .transport-item:last-child {
            border-bottom: none;
        }

        .transport-icon {
            width: 40px;
            height: 40px;
            background: rgba(70, 130, 180, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .transport-icon i {
            color: #4682B4;
            font-size: 16px;
        }

        .transport-info strong {
            color: #333;
            font-size: 14px;
            display: block;
            margin-bottom: 5px;
        }

        .transport-info p {
            color: #666;
            font-size: 13px;
            margin: 0 0 3px 0;
        }

        .transport-info small {
            color: #4682B4;
            font-size: 11px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin: 30px 0;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
        }

        .action-btn.secondary {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
            border: 1px solid rgba(70, 130, 180, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .contact-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .contact-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-item strong {
            color: #333;
            font-size: 14px;
        }

        .contact-item span {
            color: #4682B4;
            font-size: 14px;
            font-weight: 500;
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function viewMap() {
            alert('地图导航\n（演示功能）\n将打开地图应用显示酒店位置和路线');
        }

        function callHotel() {
            if (confirm('是否拨打酒店电话？\n020-87654321')) {
                window.location.href = 'tel:020-87654321';
            }
        }

        function modifyBooking() {
            alert('修改预订\n（演示功能）\n在实际应用中，这里会跳转到预订修改页面');
        }
    </script>
</body>
</html>
