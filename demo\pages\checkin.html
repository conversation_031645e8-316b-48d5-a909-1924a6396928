<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签到 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>签到</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-check-circle"></i>
                    <h2>现场签到</h2>
                    <p>企业管理现场会签到系统</p>
                </div>

                <!-- 签到状态 -->
                <div class="checkin-status">
                    <div class="status-card" id="statusCard">
                        <div class="status-icon">
                            <i class="fas fa-clock" id="statusIcon"></i>
                        </div>
                        <div class="status-info">
                            <h3 id="statusTitle">未签到</h3>
                            <p id="statusDesc">请点击下方按钮进行签到</p>
                            <small id="statusTime"></small>
                        </div>
                    </div>
                </div>

                <!-- 签到按钮 -->
                <div class="checkin-action">
                    <button class="checkin-btn" id="checkinBtn" onclick="performCheckin()">
                        <i class="fas fa-qrcode"></i>
                        <span>扫码签到</span>
                    </button>
                    
                    <button class="manual-checkin-btn" onclick="manualCheckin()">
                        <i class="fas fa-edit"></i>
                        <span>手动签到</span>
                    </button>
                </div>

                <!-- 签到信息 -->
                <div class="checkin-info">
                    <h3>会议信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <div>
                                <strong>会议时间</strong>
                                <p>2025年9月15日-16日</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>会议地点</strong>
                                <p>广东烟草大厦会议中心</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-users"></i>
                            <div>
                                <strong>参会人数</strong>
                                <p>已签到 <span id="checkedCount">0</span> / 总计 120 人</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>签到时间</strong>
                                <p>08:30 - 09:00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 签到记录 -->
                <div class="checkin-history" id="checkinHistory" style="display: none;">
                    <h3>签到记录</h3>
                    <div class="history-list" id="historyList">
                        <!-- 签到记录将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .checkin-status {
            margin: 30px 0;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #ffc107;
        }

        .status-card.checked {
            border-left-color: #28a745;
        }

        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #ffc107;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
        }

        .status-icon.checked {
            background: #28a745;
        }

        .status-icon i {
            font-size: 24px;
            color: white;
        }

        .status-info h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .status-info p {
            color: #666;
            margin-bottom: 5px;
        }

        .status-info small {
            color: #999;
            font-size: 12px;
        }

        .checkin-action {
            text-align: center;
            margin: 30px 0;
        }

        .checkin-btn, .manual-checkin-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .checkin-btn {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
        }

        .manual-checkin-btn {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
            border: 2px solid rgba(70, 130, 180, 0.3);
        }

        .checkin-btn:hover, .manual-checkin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .checkin-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .checkin-info {
            margin: 30px 0;
        }

        .checkin-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .info-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .info-item i {
            font-size: 20px;
            color: #4682B4;
            margin-right: 15px;
            width: 30px;
        }

        .info-item strong {
            display: block;
            color: #333;
            font-size: 14px;
            margin-bottom: 3px;
        }

        .info-item p {
            color: #666;
            font-size: 13px;
            margin: 0;
        }

        .checkin-history {
            margin-top: 30px;
        }

        .checkin-history h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .history-list {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .history-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-item .time {
            color: #4682B4;
            font-size: 12px;
            font-weight: 500;
        }
    </style>

    <script>
        let isCheckedIn = false;

        function goBack() {
            window.history.back();
        }

        function performCheckin() {
            if (isCheckedIn) return;

            const btn = document.getElementById('checkinBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>签到中...</span>';
            btn.disabled = true;

            // 模拟签到过程
            setTimeout(() => {
                isCheckedIn = true;
                updateCheckinStatus();
                addCheckinRecord();
                
                btn.innerHTML = '<i class="fas fa-check"></i> <span>已签到</span>';
                btn.style.background = '#28a745';
                
                // 更新签到人数
                const countElement = document.getElementById('checkedCount');
                countElement.textContent = parseInt(countElement.textContent) + 1;
                
                alert('签到成功！');
            }, 2000);
        }

        function manualCheckin() {
            if (isCheckedIn) {
                alert('您已经签到过了！');
                return;
            }

            const name = prompt('请输入您的姓名进行手动签到：');
            if (name && name.trim()) {
                isCheckedIn = true;
                updateCheckinStatus();
                addCheckinRecord(name.trim());
                
                const btn = document.getElementById('checkinBtn');
                btn.innerHTML = '<i class="fas fa-check"></i> <span>已签到</span>';
                btn.style.background = '#28a745';
                btn.disabled = true;
                
                // 更新签到人数
                const countElement = document.getElementById('checkedCount');
                countElement.textContent = parseInt(countElement.textContent) + 1;
                
                alert('手动签到成功！');
            }
        }

        function updateCheckinStatus() {
            const statusCard = document.getElementById('statusCard');
            const statusIcon = document.getElementById('statusIcon');
            const statusTitle = document.getElementById('statusTitle');
            const statusDesc = document.getElementById('statusDesc');
            const statusTime = document.getElementById('statusTime');

            statusCard.classList.add('checked');
            statusIcon.classList.add('checked');
            statusIcon.innerHTML = '<i class="fas fa-check"></i>';
            statusTitle.textContent = '已签到';
            statusDesc.textContent = '签到成功，欢迎参加会议！';
            statusTime.textContent = `签到时间：${new Date().toLocaleString()}`;
        }

        function addCheckinRecord(name = '张三') {
            const historyDiv = document.getElementById('checkinHistory');
            const historyList = document.getElementById('historyList');
            
            const recordItem = document.createElement('div');
            recordItem.className = 'history-item';
            recordItem.innerHTML = `
                <div>
                    <strong>${name}</strong>
                    <p>签到成功</p>
                </div>
                <div class="time">${new Date().toLocaleTimeString()}</div>
            `;
            
            historyList.appendChild(recordItem);
            historyDiv.style.display = 'block';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟已有签到人数
            document.getElementById('checkedCount').textContent = Math.floor(Math.random() * 50) + 30;
        });
    </script>
</body>
</html>
