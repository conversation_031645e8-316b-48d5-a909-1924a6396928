<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的用餐 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>我的用餐</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-utensils"></i>
                    <h2>用餐安排</h2>
                    <p>会议期间的餐饮服务信息</p>
                </div>

                <!-- 用餐状态卡片 -->
                <div class="dining-status">
                    <div class="status-card">
                        <div class="status-header">
                            <h3>今日用餐状态</h3>
                            <span class="date">2025年9月15日</span>
                        </div>
                        <div class="meal-progress">
                            <div class="meal-item">
                                <div class="meal-info">
                                    <i class="fas fa-coffee"></i>
                                    <span>早餐</span>
                                </div>
                                <div class="meal-status completed">
                                    <i class="fas fa-check"></i>
                                    已用餐
                                </div>
                            </div>
                            <div class="meal-item">
                                <div class="meal-info">
                                    <i class="fas fa-utensils"></i>
                                    <span>午餐</span>
                                </div>
                                <div class="meal-status pending">
                                    <i class="fas fa-clock"></i>
                                    待用餐
                                </div>
                            </div>
                            <div class="meal-item">
                                <div class="meal-info">
                                    <i class="fas fa-moon"></i>
                                    <span>晚餐</span>
                                </div>
                                <div class="meal-status pending">
                                    <i class="fas fa-clock"></i>
                                    待用餐
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 餐券信息 -->
                <div class="meal-tickets">
                    <h3>我的餐券</h3>
                    <div class="ticket-list">
                        <div class="ticket-item used">
                            <div class="ticket-header">
                                <div class="ticket-type">
                                    <i class="fas fa-coffee"></i>
                                    <span>早餐券</span>
                                </div>
                                <div class="ticket-status">已使用</div>
                            </div>
                            <div class="ticket-details">
                                <p><strong>时间：</strong>07:30 - 08:30</p>
                                <p><strong>地点：</strong>大厦1楼餐厅</p>
                                <p><strong>使用时间：</strong>08:15</p>
                            </div>
                        </div>

                        <div class="ticket-item available">
                            <div class="ticket-header">
                                <div class="ticket-type">
                                    <i class="fas fa-utensils"></i>
                                    <span>午餐券</span>
                                </div>
                                <div class="ticket-status">可使用</div>
                            </div>
                            <div class="ticket-details">
                                <p><strong>时间：</strong>12:00 - 13:30</p>
                                <p><strong>地点：</strong>大厦1楼餐厅</p>
                                <p><strong>菜单：</strong>商务套餐A</p>
                            </div>
                            <button class="use-ticket-btn" onclick="useTicket('lunch')">
                                <i class="fas fa-qrcode"></i>
                                使用餐券
                            </button>
                        </div>

                        <div class="ticket-item available">
                            <div class="ticket-header">
                                <div class="ticket-type">
                                    <i class="fas fa-moon"></i>
                                    <span>晚餐券</span>
                                </div>
                                <div class="ticket-status">可使用</div>
                            </div>
                            <div class="ticket-details">
                                <p><strong>时间：</strong>18:00 - 19:30</p>
                                <p><strong>地点：</strong>大厦2楼宴会厅</p>
                                <p><strong>菜单：</strong>欢迎晚宴</p>
                            </div>
                            <button class="use-ticket-btn" onclick="useTicket('dinner')">
                                <i class="fas fa-qrcode"></i>
                                使用餐券
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 菜单预览 -->
                <div class="menu-preview">
                    <h3>今日菜单</h3>
                    <div class="menu-tabs">
                        <button class="menu-tab active" onclick="switchMenu('lunch')">午餐</button>
                        <button class="menu-tab" onclick="switchMenu('dinner')">晚餐</button>
                    </div>
                    
                    <div class="menu-content" id="lunchMenu">
                        <div class="menu-section">
                            <h4>商务套餐A</h4>
                            <div class="dish-list">
                                <div class="dish-item">
                                    <span>白切鸡</span>
                                    <small>精选土鸡，口感鲜嫩</small>
                                </div>
                                <div class="dish-item">
                                    <span>清蒸鲈鱼</span>
                                    <small>新鲜鲈鱼，营养丰富</small>
                                </div>
                                <div class="dish-item">
                                    <span>时令蔬菜</span>
                                    <small>当季新鲜蔬菜</small>
                                </div>
                                <div class="dish-item">
                                    <span>白米饭</span>
                                    <small>优质大米</small>
                                </div>
                                <div class="dish-item">
                                    <span>例汤</span>
                                    <small>营养汤品</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="menu-content" id="dinnerMenu" style="display: none;">
                        <div class="menu-section">
                            <h4>欢迎晚宴</h4>
                            <div class="dish-list">
                                <div class="dish-item">
                                    <span>红烧狮子头</span>
                                    <small>传统名菜，肉质鲜美</small>
                                </div>
                                <div class="dish-item">
                                    <span>蒜蓉扇贝</span>
                                    <small>新鲜扇贝，蒜香浓郁</small>
                                </div>
                                <div class="dish-item">
                                    <span>宫保鸡丁</span>
                                    <small>经典川菜，香辣可口</small>
                                </div>
                                <div class="dish-item">
                                    <span>麻婆豆腐</span>
                                    <small>嫩滑豆腐，麻辣鲜香</small>
                                </div>
                                <div class="dish-item">
                                    <span>水果拼盘</span>
                                    <small>时令水果</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 特殊需求 -->
                <div class="special-requirements">
                    <h3>特殊饮食需求</h3>
                    <div class="requirement-form">
                        <div class="form-group">
                            <label>饮食偏好</label>
                            <select id="dietaryPreference">
                                <option value="normal">无特殊要求</option>
                                <option value="vegetarian">素食</option>
                                <option value="halal">清真</option>
                                <option value="low-salt">少盐</option>
                                <option value="low-oil">少油</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>过敏信息</label>
                            <input type="text" id="allergyInfo" placeholder="请输入过敏食物（如：海鲜、花生等）">
                        </div>
                        <button class="submit-btn" onclick="submitRequirements()">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .dining-status {
            margin: 20px 0;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-header h3 {
            color: #333;
            font-size: 16px;
            margin: 0;
        }

        .status-header .date {
            color: #4682B4;
            font-size: 12px;
            font-weight: 500;
        }

        .meal-progress {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .meal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .meal-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .meal-info i {
            color: #4682B4;
            font-size: 16px;
        }

        .meal-info span {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .meal-status {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        .meal-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .meal-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .meal-tickets {
            margin: 30px 0;
        }

        .meal-tickets h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .ticket-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .ticket-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #4682B4;
        }

        .ticket-item.used {
            border-left-color: #6c757d;
            opacity: 0.7;
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .ticket-type {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ticket-type i {
            color: #4682B4;
            font-size: 18px;
        }

        .ticket-type span {
            color: #333;
            font-size: 16px;
            font-weight: 500;
        }

        .ticket-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .ticket-item.used .ticket-status {
            background: #e9ecef;
            color: #6c757d;
        }

        .ticket-item.available .ticket-status {
            background: #d4edda;
            color: #155724;
        }

        .ticket-details p {
            color: #666;
            font-size: 13px;
            margin: 5px 0;
        }

        .use-ticket-btn {
            width: 100%;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .use-ticket-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .menu-preview {
            margin: 30px 0;
        }

        .menu-preview h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .menu-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .menu-tab {
            flex: 1;
            background: none;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .menu-tab.active {
            background: #4682B4;
            color: white;
        }

        .menu-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .menu-section h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #4682B4;
        }

        .dish-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .dish-item {
            display: flex;
            flex-direction: column;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .dish-item span {
            color: #333;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 3px;
        }

        .dish-item small {
            color: #666;
            font-size: 12px;
        }

        .special-requirements {
            margin: 30px 0;
        }

        .special-requirements h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .requirement-form {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function useTicket(mealType) {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 使用中...';
            btn.disabled = true;

            setTimeout(() => {
                alert(`${mealType === 'lunch' ? '午餐' : '晚餐'}券使用成功！\n请前往指定餐厅用餐。`);
                
                // 更新按钮状态
                const ticketItem = btn.closest('.ticket-item');
                ticketItem.classList.remove('available');
                ticketItem.classList.add('used');
                
                const statusElement = ticketItem.querySelector('.ticket-status');
                statusElement.textContent = '已使用';
                statusElement.style.background = '#e9ecef';
                statusElement.style.color = '#6c757d';
                
                btn.remove();
                
                // 更新用餐状态
                updateMealStatus(mealType);
            }, 2000);
        }

        function updateMealStatus(mealType) {
            const mealItems = document.querySelectorAll('.meal-item');
            mealItems.forEach(item => {
                const mealName = item.querySelector('.meal-info span').textContent;
                if ((mealType === 'lunch' && mealName === '午餐') || 
                    (mealType === 'dinner' && mealName === '晚餐')) {
                    const status = item.querySelector('.meal-status');
                    status.className = 'meal-status completed';
                    status.innerHTML = '<i class="fas fa-check"></i> 已用餐';
                }
            });
        }

        function switchMenu(menuType) {
            // 隐藏所有菜单
            document.querySelectorAll('.menu-content').forEach(menu => {
                menu.style.display = 'none';
            });
            
            // 移除所有活动状态
            document.querySelectorAll('.menu-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的菜单
            document.getElementById(menuType + 'Menu').style.display = 'block';
            
            // 添加活动状态
            event.target.classList.add('active');
        }

        function submitRequirements() {
            const preference = document.getElementById('dietaryPreference').value;
            const allergy = document.getElementById('allergyInfo').value;
            
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            btn.disabled = true;

            setTimeout(() => {
                alert('特殊饮食需求已保存！\n我们会根据您的要求安排合适的餐食。');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1500);
        }
    </script>
</body>
</html>
