<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云直播 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>云直播</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-broadcast-tower"></i>
                    <h2>云直播</h2>
                    <p>企业管理现场会实时直播</p>
                </div>

                <!-- 直播状态 -->
                <div class="live-status">
                    <div class="status-card" id="liveStatus">
                        <div class="status-indicator">
                            <div class="live-dot"></div>
                            <span id="statusText">即将开始</span>
                        </div>
                        <div class="viewer-count">
                            <i class="fas fa-eye"></i>
                            <span id="viewerCount">0</span> 人观看
                        </div>
                    </div>
                </div>

                <!-- 直播窗口 -->
                <div class="live-player">
                    <div class="player-container" id="playerContainer">
                        <div class="player-placeholder">
                            <i class="fas fa-play-circle"></i>
                            <h3>直播即将开始</h3>
                            <p>请稍候，直播将在会议开始时自动播放</p>
                        </div>
                    </div>
                    <div class="player-controls">
                        <button class="control-btn" id="playBtn" onclick="togglePlay()">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" onclick="toggleMute()">
                            <i class="fas fa-volume-up" id="volumeIcon"></i>
                        </button>
                        <button class="control-btn" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="control-btn" onclick="shareStream()">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>

                <!-- 直播信息 -->
                <div class="live-info">
                    <h3>直播信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <div>
                                <strong>直播时间</strong>
                                <p>2025年9月15日 09:00 - 17:00</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-microphone"></i>
                            <div>
                                <strong>当前议程</strong>
                                <p id="currentAgenda">开幕式致辞</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <div>
                                <strong>主讲人</strong>
                                <p id="currentSpeaker">公司领导</p>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <div>
                                <strong>直播质量</strong>
                                <p>
                                    <select id="qualitySelect" onchange="changeQuality()">
                                        <option value="auto">自动</option>
                                        <option value="1080p">1080P 高清</option>
                                        <option value="720p">720P 标清</option>
                                        <option value="480p">480P 流畅</option>
                                    </select>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 互动区域 -->
                <div class="interaction-area">
                    <h3>互动交流</h3>
                    
                    <!-- 聊天区域 -->
                    <div class="chat-section">
                        <div class="chat-messages" id="chatMessages">
                            <div class="chat-message system">
                                <span class="message-text">欢迎观看企业管理现场会直播！</span>
                            </div>
                        </div>
                        <div class="chat-input">
                            <input type="text" id="messageInput" placeholder="输入您的评论..." onkeypress="handleKeyPress(event)">
                            <button onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 互动统计 -->
                    <div class="interaction-stats">
                        <div class="stat-item">
                            <button class="like-btn" onclick="likeStream()">
                                <i class="fas fa-thumbs-up"></i>
                                <span id="likeCount">0</span>
                            </button>
                        </div>
                        <div class="stat-item">
                            <span><i class="fas fa-comments"></i> <span id="commentCount">0</span> 条评论</span>
                        </div>
                    </div>
                </div>

                <!-- 相关链接 -->
                <div class="related-links">
                    <h3>相关功能</h3>
                    <div class="links-grid">
                        <button class="link-btn" onclick="goToAgenda()">
                            <i class="fas fa-calendar-alt"></i>
                            <span>查看议程</span>
                        </button>
                        <button class="link-btn" onclick="goToMaterials()">
                            <i class="fas fa-file-alt"></i>
                            <span>会议资料</span>
                        </button>
                        <button class="link-btn" onclick="goToOnlineMeeting()">
                            <i class="fas fa-video"></i>
                            <span>线上参会</span>
                        </button>
                        <button class="link-btn" onclick="goToPhotos()">
                            <i class="fas fa-images"></i>
                            <span>在线相册</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .live-status {
            margin: 20px 0;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .live-dot {
            width: 12px;
            height: 12px;
            background: #ff4757;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .live-dot.offline {
            background: #ccc;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-indicator span {
            color: #333;
            font-weight: 500;
        }

        .viewer-count {
            color: #666;
            font-size: 14px;
        }

        .viewer-count i {
            color: #4682B4;
            margin-right: 5px;
        }

        .live-player {
            margin: 20px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .player-container {
            width: 100%;
            height: 200px;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .player-placeholder {
            text-align: center;
            color: white;
        }

        .player-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .player-placeholder h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .player-placeholder p {
            font-size: 14px;
            opacity: 0.8;
        }

        .player-controls {
            background: #f8f9fa;
            padding: 15px;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .control-btn {
            background: none;
            border: none;
            color: #4682B4;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(70, 130, 180, 0.1);
        }

        .live-info {
            margin: 30px 0;
        }

        .live-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .info-item i {
            color: #4682B4;
            font-size: 18px;
            width: 20px;
        }

        .info-item strong {
            color: #333;
            font-size: 14px;
            display: block;
            margin-bottom: 3px;
        }

        .info-item p {
            color: #666;
            font-size: 13px;
            margin: 0;
        }

        #qualitySelect {
            background: rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.3);
            border-radius: 5px;
            padding: 3px 8px;
            font-size: 12px;
            color: #4682B4;
        }

        .interaction-area {
            margin: 30px 0;
        }

        .interaction-area h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .chat-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
        }

        .chat-messages {
            height: 150px;
            overflow-y: auto;
            padding: 15px;
            background: #f8f9fa;
        }

        .chat-message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 13px;
        }

        .chat-message.system {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
            text-align: center;
        }

        .chat-message.user {
            background: white;
            border-left: 3px solid #4682B4;
        }

        .chat-input {
            display: flex;
            padding: 15px;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .chat-input button {
            background: #4682B4;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chat-input button:hover {
            background: #1E90FF;
        }

        .interaction-stats {
            display: flex;
            justify-content: space-around;
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .like-btn {
            background: none;
            border: none;
            color: #4682B4;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .like-btn:hover {
            color: #ff4757;
        }

        .like-btn.liked {
            color: #ff4757;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 14px;
        }

        .stat-item i {
            color: #4682B4;
        }

        .related-links {
            margin: 30px 0;
        }

        .related-links h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .link-btn {
            background: white;
            border: 1px solid rgba(70, 130, 180, 0.3);
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: #4682B4;
        }

        .link-btn:hover {
            background: rgba(70, 130, 180, 0.1);
            transform: translateY(-2px);
        }

        .link-btn i {
            font-size: 20px;
        }

        .link-btn span {
            font-size: 12px;
            font-weight: 500;
        }
    </style>

    <script>
        let isPlaying = false;
        let isMuted = false;
        let likeCount = 0;
        let commentCount = 0;
        let viewerCount = 0;

        function goBack() {
            window.history.back();
        }

        function togglePlay() {
            const playBtn = document.getElementById('playBtn');
            const statusText = document.getElementById('statusText');
            
            if (isPlaying) {
                playBtn.innerHTML = '<i class="fas fa-play"></i>';
                statusText.textContent = '已暂停';
                isPlaying = false;
            } else {
                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
                statusText.textContent = '直播中';
                isPlaying = true;
            }
        }

        function toggleMute() {
            const volumeIcon = document.getElementById('volumeIcon');
            
            if (isMuted) {
                volumeIcon.className = 'fas fa-volume-up';
                isMuted = false;
            } else {
                volumeIcon.className = 'fas fa-volume-mute';
                isMuted = true;
            }
        }

        function toggleFullscreen() {
            alert('全屏功能\n（演示功能）\n在实际应用中会切换到全屏模式');
        }

        function shareStream() {
            if (navigator.share) {
                navigator.share({
                    title: '企业管理现场会直播',
                    text: '正在观看企业管理现场会直播',
                    url: window.location.href
                });
            } else {
                alert('分享直播\n（演示功能）\n在实际应用中会调用分享功能');
            }
        }

        function changeQuality() {
            const quality = document.getElementById('qualitySelect').value;
            alert(`切换到${quality}画质\n（演示功能）`);
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                const chatMessages = document.getElementById('chatMessages');
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message user';
                messageElement.innerHTML = `<strong>我：</strong> ${message}`;
                
                chatMessages.appendChild(messageElement);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                input.value = '';
                commentCount++;
                document.getElementById('commentCount').textContent = commentCount;
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function likeStream() {
            const likeBtn = document.querySelector('.like-btn');
            likeBtn.classList.add('liked');
            likeCount++;
            document.getElementById('likeCount').textContent = likeCount;
        }

        function goToAgenda() {
            window.location.href = 'agenda.html';
        }

        function goToMaterials() {
            window.location.href = 'materials.html';
        }

        function goToOnlineMeeting() {
            window.location.href = 'online-meeting.html';
        }

        function goToPhotos() {
            window.location.href = 'photo.html';
        }

        // 模拟观看人数变化
        function updateViewerCount() {
            viewerCount = Math.floor(Math.random() * 50) + 100;
            document.getElementById('viewerCount').textContent = viewerCount;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateViewerCount();
            setInterval(updateViewerCount, 30000); // 每30秒更新一次观看人数
            
            // 模拟自动消息
            setTimeout(() => {
                const chatMessages = document.getElementById('chatMessages');
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message user';
                messageElement.innerHTML = '<strong>张经理：</strong> 演讲内容很精彩！';
                chatMessages.appendChild(messageElement);
                commentCount++;
                document.getElementById('commentCount').textContent = commentCount;
            }, 5000);
        });
    </script>
</body>
</html>
