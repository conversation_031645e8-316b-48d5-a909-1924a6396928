<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议资料 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>会议资料</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-file-alt"></i>
                    <h2>会议资料</h2>
                    <p>企业管理现场会相关文档资料</p>
                </div>

                <!-- 资料分类 -->
                <div class="material-categories">
                    <button class="category-btn active" onclick="switchCategory('all')">全部</button>
                    <button class="category-btn" onclick="switchCategory('presentation')">演讲资料</button>
                    <button class="category-btn" onclick="switchCategory('document')">会议文档</button>
                    <button class="category-btn" onclick="switchCategory('handbook')">参会手册</button>
                </div>

                <!-- 资料列表 -->
                <div class="materials-list" id="materialsList">
                    <!-- 资料项目将在这里动态生成 -->
                </div>

                <!-- 统计信息 -->
                <div class="materials-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>总资料数：<strong id="totalMaterials">0</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-download"></i>
                        <span>下载次数：<strong id="totalDownloads">0</strong></span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .material-categories {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            overflow-x: auto;
            padding: 5px 0;
        }

        .category-btn {
            background: rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.3);
            color: #4682B4;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .category-btn.active,
        .category-btn:hover {
            background: #4682B4;
            color: white;
        }

        .materials-list {
            margin: 20px 0;
        }

        .material-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #4682B4;
            transition: all 0.3s ease;
        }

        .material-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .material-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .material-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .material-icon i {
            color: white;
            font-size: 20px;
        }

        .material-info {
            flex: 1;
        }

        .material-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .material-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #666;
        }

        .material-meta span {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .material-description {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
            margin: 15px 0;
        }

        .material-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .download-btn {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
        }

        .preview-btn {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
            border: 1px solid rgba(70, 130, 180, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
        }

        .materials-stats {
            display: flex;
            justify-content: space-around;
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .stat-item i {
            color: #4682B4;
        }

        .stat-item strong {
            color: #333;
        }

        .material-tag {
            background: rgba(70, 130, 180, 0.1);
            color: #4682B4;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin-left: 10px;
        }
    </style>

    <script>
        let currentCategory = 'all';
        let materials = [];

        function goBack() {
            window.history.back();
        }

        function switchCategory(category) {
            currentCategory = category;
            
            // 更新按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 过滤并显示资料
            filterMaterials();
        }

        function filterMaterials() {
            const filteredMaterials = currentCategory === 'all' 
                ? materials 
                : materials.filter(material => material.category === currentCategory);
            
            displayMaterials(filteredMaterials);
        }

        function displayMaterials(materialsToShow) {
            const list = document.getElementById('materialsList');
            list.innerHTML = '';
            
            materialsToShow.forEach(material => {
                const materialElement = document.createElement('div');
                materialElement.className = 'material-item';
                
                materialElement.innerHTML = `
                    <div class="material-header">
                        <div class="material-icon">
                            <i class="${material.icon}"></i>
                        </div>
                        <div class="material-info">
                            <h3>${material.title}<span class="material-tag">${material.type}</span></h3>
                            <div class="material-meta">
                                <span><i class="fas fa-calendar"></i> ${material.date}</span>
                                <span><i class="fas fa-file"></i> ${material.size}</span>
                                <span><i class="fas fa-download"></i> ${material.downloads}次</span>
                            </div>
                        </div>
                    </div>
                    <div class="material-description">
                        ${material.description}
                    </div>
                    <div class="material-actions">
                        <button class="action-btn download-btn" onclick="downloadMaterial('${material.id}')">
                            <i class="fas fa-download"></i>
                            下载
                        </button>
                        <button class="action-btn preview-btn" onclick="previewMaterial('${material.id}')">
                            <i class="fas fa-eye"></i>
                            预览
                        </button>
                    </div>
                `;
                
                list.appendChild(materialElement);
            });
        }

        function generateMaterials() {
            materials = [
                {
                    id: '1',
                    title: '数智攀登管理跃升主题演讲',
                    category: 'presentation',
                    type: 'PPT',
                    icon: 'fas fa-file-powerpoint',
                    date: '2025-09-15',
                    size: '15.2MB',
                    downloads: 156,
                    description: '企业数字化转型与智能化管理的创新实践分享，包含最新的管理理念和实施方案。'
                },
                {
                    id: '2',
                    title: '企业管理现场会议程安排',
                    category: 'document',
                    type: 'PDF',
                    icon: 'fas fa-file-pdf',
                    date: '2025-09-10',
                    size: '2.8MB',
                    downloads: 234,
                    description: '详细的会议日程安排，包含所有演讲主题、时间安排和会场信息。'
                },
                {
                    id: '3',
                    title: '参会人员手册',
                    category: 'handbook',
                    type: 'PDF',
                    icon: 'fas fa-book',
                    date: '2025-09-08',
                    size: '8.5MB',
                    downloads: 189,
                    description: '参会须知、交通指南、住宿信息、联系方式等重要信息汇总。'
                },
                {
                    id: '4',
                    title: '企业数字化转型案例分析',
                    category: 'presentation',
                    type: 'PPT',
                    icon: 'fas fa-file-powerpoint',
                    date: '2025-09-14',
                    size: '22.1MB',
                    downloads: 98,
                    description: '多个成功企业的数字化转型案例深度分析，提供可借鉴的实施经验。'
                },
                {
                    id: '5',
                    title: '会议签到表模板',
                    category: 'document',
                    type: 'Excel',
                    icon: 'fas fa-file-excel',
                    date: '2025-09-12',
                    size: '1.2MB',
                    downloads: 67,
                    description: '标准化的会议签到表格模板，便于各部门统计参会情况。'
                },
                {
                    id: '6',
                    title: '智能管理系统操作指南',
                    category: 'handbook',
                    type: 'PDF',
                    icon: 'fas fa-book',
                    date: '2025-09-13',
                    size: '12.3MB',
                    downloads: 145,
                    description: '新一代智能管理系统的详细操作说明，帮助快速掌握系统使用方法。'
                }
            ];
            
            displayMaterials(materials);
            updateStats();
        }

        function updateStats() {
            document.getElementById('totalMaterials').textContent = materials.length;
            document.getElementById('totalDownloads').textContent = 
                materials.reduce((sum, material) => sum + material.downloads, 0);
        }

        function downloadMaterial(materialId) {
            const material = materials.find(m => m.id === materialId);
            if (material) {
                // 模拟下载
                alert(`正在下载：${material.title}\n文件大小：${material.size}\n（这是演示功能）`);
                
                // 增加下载次数
                material.downloads++;
                filterMaterials();
                updateStats();
            }
        }

        function previewMaterial(materialId) {
            const material = materials.find(m => m.id === materialId);
            if (material) {
                alert(`预览：${material.title}\n（演示功能）\n在实际应用中，这里会打开文档预览窗口`);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateMaterials();
        });
    </script>
</body>
</html>
