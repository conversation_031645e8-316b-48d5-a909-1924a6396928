<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线上参会 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>线上参会</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-video"></i>
                    <h2>线上参会</h2>
                    <p>远程参与企业管理现场会</p>
                </div>

                <!-- 会议状态 -->
                <div class="meeting-status">
                    <div class="status-card" id="meetingStatus">
                        <div class="status-icon">
                            <i class="fas fa-clock" id="statusIcon"></i>
                        </div>
                        <div class="status-info">
                            <h3 id="statusTitle">会议即将开始</h3>
                            <p id="statusDesc">距离会议开始还有</p>
                            <div class="countdown" id="countdown">
                                <span id="days">00</span>天
                                <span id="hours">00</span>时
                                <span id="minutes">00</span>分
                                <span id="seconds">00</span>秒
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 会议入口 -->
                <div class="meeting-entrance">
                    <h3>会议入口</h3>
                    <div class="entrance-options">
                        <div class="entrance-item">
                            <div class="entrance-icon">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="entrance-info">
                                <h4>电脑端参会</h4>
                                <p>推荐使用电脑浏览器参会，体验更佳</p>
                            </div>
                            <button class="entrance-btn" onclick="joinMeeting('desktop')">
                                <i class="fas fa-external-link-alt"></i>
                                进入会议
                            </button>
                        </div>

                        <div class="entrance-item">
                            <div class="entrance-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="entrance-info">
                                <h4>手机端参会</h4>
                                <p>随时随地参与会议，移动便捷</p>
                            </div>
                            <button class="entrance-btn" onclick="joinMeeting('mobile')">
                                <i class="fas fa-external-link-alt"></i>
                                进入会议
                            </button>
                        </div>

                        <div class="entrance-item">
                            <div class="entrance-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="entrance-info">
                                <h4>电话参会</h4>
                                <p>网络不佳时可选择电话接入</p>
                            </div>
                            <button class="entrance-btn" onclick="joinMeeting('phone')">
                                <i class="fas fa-phone"></i>
                                拨号参会
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 会议信息 -->
                <div class="meeting-info">
                    <h3>会议信息</h3>
                    <div class="info-card">
                        <div class="info-item">
                            <strong>会议ID：</strong>
                            <span>123 456 789</span>
                            <button class="copy-btn" onclick="copyToClipboard('123456789')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="info-item">
                            <strong>会议密码：</strong>
                            <span>gdtobacco2025</span>
                            <button class="copy-btn" onclick="copyToClipboard('gdtobacco2025')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="info-item">
                            <strong>主持人：</strong>
                            <span>会务组</span>
                        </div>
                        <div class="info-item">
                            <strong>会议时长：</strong>
                            <span>2天（9月15日-16日）</span>
                        </div>
                    </div>
                </div>

                <!-- 技术要求 -->
                <div class="tech-requirements">
                    <h3>技术要求</h3>
                    <div class="requirements-list">
                        <div class="requirement-item">
                            <i class="fas fa-wifi"></i>
                            <div>
                                <strong>网络要求</strong>
                                <p>建议使用稳定的宽带网络，上行带宽不低于1Mbps</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <i class="fas fa-microphone"></i>
                            <div>
                                <strong>音频设备</strong>
                                <p>确保麦克风和扬声器正常工作，建议使用耳机</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <i class="fas fa-video"></i>
                            <div>
                                <strong>视频设备</strong>
                                <p>如需开启摄像头，请确保摄像头设备正常</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <i class="fas fa-browser"></i>
                            <div>
                                <strong>浏览器支持</strong>
                                <p>推荐使用Chrome、Firefox、Safari等现代浏览器</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参会须知 -->
                <div class="meeting-rules">
                    <h3>参会须知</h3>
                    <div class="rules-list">
                        <div class="rule-item">
                            <i class="fas fa-microphone-slash"></i>
                            <span>进入会议后请先静音，避免干扰</span>
                        </div>
                        <div class="rule-item">
                            <i class="fas fa-hand-paper"></i>
                            <span>发言时请先举手申请，获得许可后再发言</span>
                        </div>
                        <div class="rule-item">
                            <i class="fas fa-comments"></i>
                            <span>可使用聊天功能进行文字交流和提问</span>
                        </div>
                        <div class="rule-item">
                            <i class="fas fa-user-tie"></i>
                            <span>请保持专业形象，注意会议礼仪</span>
                        </div>
                    </div>
                </div>

                <!-- 技术支持 -->
                <div class="tech-support">
                    <h3>技术支持</h3>
                    <div class="support-info">
                        <p>如遇技术问题，请联系技术支持：</p>
                        <div class="support-contact">
                            <button class="support-btn" onclick="callSupport()">
                                <i class="fas fa-phone"></i>
                                ************
                            </button>
                            <button class="support-btn" onclick="chatSupport()">
                                <i class="fas fa-comments"></i>
                                在线客服
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .meeting-status {
            margin: 20px 0;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            gap: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #4682B4;
        }

        .status-icon {
            width: 60px;
            height: 60px;
            background: #4682B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-icon i {
            color: white;
            font-size: 24px;
        }

        .status-info h3 {
            color: #333;
            font-size: 18px;
            margin-bottom: 8px;
        }

        .status-info p {
            color: #666;
            margin-bottom: 10px;
        }

        .countdown {
            display: flex;
            gap: 10px;
            font-weight: bold;
            color: #4682B4;
        }

        .countdown span {
            background: rgba(70, 130, 180, 0.1);
            padding: 5px 10px;
            border-radius: 8px;
            min-width: 30px;
            text-align: center;
        }

        .meeting-entrance, .meeting-info, .tech-requirements, .meeting-rules, .tech-support {
            margin: 30px 0;
        }

        .meeting-entrance h3, .meeting-info h3, .tech-requirements h3, .meeting-rules h3, .tech-support h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .entrance-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .entrance-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .entrance-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .entrance-icon i {
            color: white;
            font-size: 20px;
        }

        .entrance-info {
            flex: 1;
        }

        .entrance-info h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .entrance-info p {
            color: #666;
            font-size: 12px;
            margin: 0;
        }

        .entrance-btn {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .entrance-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .info-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .info-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item strong {
            color: #333;
            font-size: 14px;
        }

        .info-item span {
            color: #4682B4;
            font-size: 14px;
            font-weight: 500;
        }

        .copy-btn {
            background: rgba(70, 130, 180, 0.1);
            border: none;
            color: #4682B4;
            padding: 5px 8px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .requirements-list, .rules-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .requirement-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .requirement-item:last-child {
            border-bottom: none;
        }

        .requirement-item i {
            color: #4682B4;
            font-size: 18px;
            margin-top: 2px;
        }

        .requirement-item strong {
            color: #333;
            font-size: 14px;
            display: block;
            margin-bottom: 5px;
        }

        .requirement-item p {
            color: #666;
            font-size: 12px;
            margin: 0;
        }

        .rule-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .rule-item:last-child {
            border-bottom: none;
        }

        .rule-item i {
            color: #4682B4;
            font-size: 16px;
        }

        .rule-item span {
            color: #333;
            font-size: 13px;
        }

        .support-info {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .support-info p {
            color: #666;
            margin-bottom: 15px;
        }

        .support-contact {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .support-btn {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .support-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function joinMeeting(type) {
            let message = '';
            switch(type) {
                case 'desktop':
                    message = '正在跳转到电脑端会议室...\n（演示功能）';
                    break;
                case 'mobile':
                    message = '正在打开手机端会议应用...\n（演示功能）';
                    break;
                case 'phone':
                    message = '电话参会号码：************\n会议ID：123456789\n（演示功能）';
                    break;
            }
            alert(message);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            }).catch(() => {
                alert('复制失败，请手动复制');
            });
        }

        function callSupport() {
            window.location.href = 'tel:************';
        }

        function chatSupport() {
            alert('正在连接在线客服...\n（演示功能）');
        }

        // 倒计时功能
        function updateCountdown() {
            // 设置会议开始时间（示例：2025年9月15日 09:00）
            const meetingDate = new Date('2025-09-15T09:00:00');
            const now = new Date();
            const timeDiff = meetingDate - now;

            if (timeDiff > 0) {
                const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

                document.getElementById('days').textContent = days.toString().padStart(2, '0');
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            } else {
                // 会议已开始
                document.getElementById('statusTitle').textContent = '会议进行中';
                document.getElementById('statusDesc').textContent = '会议正在进行，点击下方按钮加入';
                document.getElementById('countdown').style.display = 'none';
                document.getElementById('statusIcon').className = 'fas fa-video';
            }
        }

        // 页面加载时开始倒计时
        document.addEventListener('DOMContentLoaded', function() {
            updateCountdown();
            setInterval(updateCountdown, 1000);
        });
    </script>
</body>
</html>
