<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线相册 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>在线相册</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-images"></i>
                    <h2>会议相册</h2>
                    <p>记录精彩瞬间，分享美好回忆</p>
                </div>

                <!-- 相册分类 -->
                <div class="album-categories">
                    <button class="category-btn active" onclick="switchCategory('all')">全部</button>
                    <button class="category-btn" onclick="switchCategory('opening')">开幕式</button>
                    <button class="category-btn" onclick="switchCategory('speech')">主题演讲</button>
                    <button class="category-btn" onclick="switchCategory('discussion')">讨论交流</button>
                    <button class="category-btn" onclick="switchCategory('group')">合影留念</button>
                </div>

                <!-- 上传按钮 -->
                <div class="upload-section">
                    <button class="upload-btn" onclick="uploadPhoto()">
                        <i class="fas fa-camera"></i>
                        上传照片
                    </button>
                </div>

                <!-- 照片网格 -->
                <div class="photo-grid" id="photoGrid">
                    <!-- 照片将在这里动态生成 -->
                </div>

                <!-- 照片统计 -->
                <div class="photo-stats">
                    <div class="stat-item">
                        <i class="fas fa-images"></i>
                        <span>总照片数：<strong id="totalPhotos">0</strong></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-heart"></i>
                        <span>总点赞数：<strong id="totalLikes">0</strong></span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 照片查看模态框 -->
    <div class="photo-modal" id="photoModal" onclick="closePhotoModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <button class="close-modal" onclick="closePhotoModal()">
                <i class="fas fa-times"></i>
            </button>
            <img id="modalImage" src="" alt="">
            <div class="photo-info">
                <h3 id="modalTitle"></h3>
                <p id="modalDesc"></p>
                <div class="photo-actions">
                    <button class="like-btn" onclick="likePhoto()">
                        <i class="fas fa-heart"></i>
                        <span id="likeCount">0</span>
                    </button>
                    <button class="share-btn" onclick="sharePhoto()">
                        <i class="fas fa-share"></i>
                        分享
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .album-categories {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            overflow-x: auto;
            padding: 5px 0;
        }

        .category-btn {
            background: rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.3);
            color: #4682B4;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .category-btn.active,
        .category-btn:hover {
            background: #4682B4;
            color: white;
        }

        .upload-section {
            text-align: center;
            margin: 20px 0;
        }

        .upload-btn {
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .photo-item:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-placeholder {
            color: #ccc;
            font-size: 24px;
        }

        .photo-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 10px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .photo-item:hover .photo-overlay {
            opacity: 1;
        }

        .photo-stats {
            display: flex;
            justify-content: space-around;
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }

        .stat-item i {
            color: #4682B4;
        }

        .stat-item strong {
            color: #333;
        }

        /* 模态框样式 */
        .photo-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .photo-modal.show {
            display: flex;
        }

        .modal-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 1001;
        }

        .modal-content img {
            width: 100%;
            height: auto;
            max-height: 60vh;
            object-fit: contain;
        }

        .photo-info {
            padding: 20px;
        }

        .photo-info h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .photo-info p {
            color: #666;
            margin-bottom: 15px;
        }

        .photo-actions {
            display: flex;
            gap: 15px;
        }

        .like-btn, .share-btn {
            background: rgba(70, 130, 180, 0.1);
            border: 1px solid rgba(70, 130, 180, 0.3);
            color: #4682B4;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .like-btn:hover, .share-btn:hover {
            background: #4682B4;
            color: white;
        }

        .like-btn.liked {
            background: #ff4757;
            border-color: #ff4757;
            color: white;
        }
    </style>

    <script>
        let currentCategory = 'all';
        let currentPhotoId = null;
        let photos = [];

        function goBack() {
            window.history.back();
        }

        function switchCategory(category) {
            currentCategory = category;
            
            // 更新按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 过滤并显示照片
            filterPhotos();
        }

        function uploadPhoto() {
            alert('上传照片功能\n（演示功能）\n在实际应用中，这里会打开相机或相册选择照片');

            // 模拟添加新照片，使用随机的会议图片
            const additionalImages = [
                'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=400&h=300&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&crop=center',
                'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=400&h=300&fit=crop&crop=center'
            ];

            const newPhoto = {
                id: Date.now(),
                title: `照片 ${photos.length + 1}`,
                category: 'group',
                likes: 0,
                description: '会议现场精彩瞬间',
                timestamp: new Date().toLocaleString(),
                imageUrl: additionalImages[Math.floor(Math.random() * additionalImages.length)]
            };

            photos.push(newPhoto);
            filterPhotos();
            updateStats();
        }

        function filterPhotos() {
            const filteredPhotos = currentCategory === 'all' 
                ? photos 
                : photos.filter(photo => photo.category === currentCategory);
            
            displayPhotos(filteredPhotos);
        }

        function displayPhotos(photosToShow) {
            const grid = document.getElementById('photoGrid');
            grid.innerHTML = '';

            photosToShow.forEach(photo => {
                const photoElement = document.createElement('div');
                photoElement.className = 'photo-item';
                photoElement.onclick = () => openPhotoModal(photo);

                photoElement.innerHTML = `
                    <img src="${photo.imageUrl}" alt="${photo.title}" loading="lazy">
                    <div class="photo-overlay">
                        <div>${photo.title}</div>
                        <div><i class="fas fa-heart"></i> ${photo.likes}</div>
                    </div>
                `;

                grid.appendChild(photoElement);
            });
        }

        function generatePhotos() {
            // 生成示例照片数据，使用真实的会议照片占位图
            const categories = ['opening', 'speech', 'discussion', 'group'];
            const titles = [
                '开幕式致辞', '主题演讲', '圆桌讨论', '合影留念',
                '会议现场', '嘉宾交流', '签到现场', '茶歇时光',
                '颁奖典礼', '闭幕式', '参观展示', '技术演示'
            ];

            // 使用Unsplash提供的会议相关图片
            const imageUrls = [
                'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop&crop=center', // 会议演讲
                'https://images.unsplash.com/photo-1559223607-b4d0555ae227?w=400&h=300&fit=crop&crop=center', // 商务会议
                'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop&crop=center', // 团队讨论
                'https://images.unsplash.com/photo-1511578314322-379afb476865?w=400&h=300&fit=crop&crop=center', // 会议室
                'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=400&h=300&fit=crop&crop=center', // 演讲台
                'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop&crop=center', // 商务握手
                'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=400&h=300&fit=crop&crop=center', // 会议现场
                'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400&h=300&fit=crop&crop=center', // 团队合作
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center', // 会议讨论
                'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=400&h=300&fit=crop&crop=center', // 商务人士
                'https://images.unsplash.com/photo-1531482615713-2afd69097998?w=400&h=300&fit=crop&crop=center', // 办公室会议
                'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=300&fit=crop&crop=center'  // 团队照片
            ];

            for (let i = 0; i < 12; i++) {
                photos.push({
                    id: i + 1,
                    title: titles[i] || `照片 ${i + 1}`,
                    category: categories[i % categories.length],
                    likes: Math.floor(Math.random() * 50),
                    description: '企业管理现场会精彩瞬间记录',
                    timestamp: new Date(Date.now() - Math.random() * 86400000).toLocaleString(),
                    imageUrl: imageUrls[i]
                });
            }

            displayPhotos(photos);
            updateStats();
        }

        function updateStats() {
            document.getElementById('totalPhotos').textContent = photos.length;
            document.getElementById('totalLikes').textContent = 
                photos.reduce((sum, photo) => sum + photo.likes, 0);
        }

        function openPhotoModal(photo) {
            currentPhotoId = photo.id;
            document.getElementById('modalImage').src = photo.imageUrl;
            document.getElementById('modalTitle').textContent = photo.title;
            document.getElementById('modalDesc').textContent = photo.description;
            document.getElementById('likeCount').textContent = photo.likes;

            document.getElementById('photoModal').classList.add('show');
        }

        function closePhotoModal() {
            document.getElementById('photoModal').classList.remove('show');
            currentPhotoId = null;
        }

        function likePhoto() {
            if (!currentPhotoId) return;
            
            const photo = photos.find(p => p.id === currentPhotoId);
            if (photo) {
                photo.likes++;
                document.getElementById('likeCount').textContent = photo.likes;
                
                const likeBtn = document.querySelector('.like-btn');
                likeBtn.classList.add('liked');
                
                updateStats();
                filterPhotos(); // 重新显示以更新点赞数
            }
        }

        function sharePhoto() {
            if (navigator.share) {
                navigator.share({
                    title: '会议照片分享',
                    text: '来自企业管理现场会的精彩照片',
                    url: window.location.href
                });
            } else {
                alert('分享功能\n（演示功能）\n在实际应用中，这里会调用系统分享功能');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            generatePhotos();
        });
    </script>
</body>
</html>
