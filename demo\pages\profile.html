<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>个人中心</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <!-- 用户信息卡片 -->
            <div class="profile-card">
                <div class="avatar-section">
                    <div class="avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-info">
                        <h2>张三</h2>
                        <p>部门经理</p>
                        <p>广东烟草广州市有限公司</p>
                    </div>
                </div>
                <div class="status-badge">
                    <i class="fas fa-check-circle"></i>
                    已报名
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="menu-container">
                <div class="menu-item" onclick="showMyInfo()">
                    <div class="menu-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的信息</span>
                        <small>查看和编辑个人信息</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showMyTickets()">
                    <div class="menu-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的门票</span>
                        <small>查看参会门票信息</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showMySchedule()">
                    <div class="menu-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的日程</span>
                        <small>个人参会日程安排</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showMyPhotos()">
                    <div class="menu-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的相册</span>
                        <small>查看会议照片</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showDining()">
                    <div class="menu-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的用餐</span>
                        <small>查看用餐安排和餐券信息</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showHotelInfo()">
                    <div class="menu-icon">
                        <i class="fas fa-bed"></i>
                    </div>
                    <div class="menu-text">
                        <span>我的住宿</span>
                        <small>查看住宿详情和房间信息</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>

                <div class="menu-item" onclick="showSettings()">
                    <div class="menu-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="menu-text">
                        <span>设置</span>
                        <small>应用设置和偏好</small>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>



            <!-- 退出登录 -->
            <div class="logout-section">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </button>
            </div>
        </main>
    </div>

    <style>
        .profile-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .avatar-section {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4682B4, #1E90FF);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .avatar i {
            font-size: 24px;
            color: white;
        }

        .user-info h2 {
            color: #333;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .user-info p {
            color: #666;
            font-size: 12px;
            margin: 2px 0;
        }

        .status-badge {
            background: #e8f5e8;
            color: #4caf50;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .menu-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 10px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }

        .menu-item:hover {
            background: rgba(70, 130, 180, 0.1);
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            background: rgba(70, 130, 180, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .menu-icon i {
            color: #4682B4;
            font-size: 16px;
        }

        .menu-text {
            flex: 1;
        }

        .menu-text span {
            display: block;
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .menu-text small {
            color: #666;
            font-size: 12px;
        }

        .menu-item > i {
            color: #ccc;
            font-size: 12px;
        }

        .stats-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .stats-container h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(70, 130, 180, 0.05);
            border-radius: 12px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4682B4;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .logout-section {
            text-align: center;
            margin-top: 30px;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 12px 30px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function showMyInfo() {
            alert('我的信息功能\n（演示功能）');
        }

        function showMyTickets() {
            alert('我的门票功能\n（演示功能）');
        }

        function showMySchedule() {
            alert('我的日程功能\n（演示功能）');
        }

        function showMyPhotos() {
            alert('我的相册功能\n（演示功能）');
        }

        function showDining() {
            // 跳转到用餐页面
            window.location.href = 'dining.html';
        }

        function showHotelInfo() {
            // 跳转到住宿页面
            window.location.href = 'accommodation.html';
        }

        function showSettings() {
            alert('设置功能\n（演示功能）');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                alert('已退出登录\n（演示功能）');
            }
        }
    </script>
</body>
</html>
