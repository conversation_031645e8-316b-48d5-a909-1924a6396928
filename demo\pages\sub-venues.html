<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分会场信息 - 企业管理现场会</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>分会场信息</h1>
            <div class="header-placeholder"></div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
            <div class="list-container">
                <div class="form-header">
                    <i class="fas fa-video"></i>
                    <h2>分会场信息</h2>
                    <p>各分会场专题内容回顾</p>
                </div>

                <!-- 分会场列表 -->
                <div class="venues-list">
                    <!-- 分会场1 -->
                    <div class="venue-card">
                        <div class="venue-header">
                            <div class="venue-info">
                                <h3>数字化转型分会场</h3>
                                <p>主题：企业数字化转型实践与案例分享</p>
                                <div class="venue-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> A会议室</span>
                                    <span><i class="fas fa-clock"></i> 90分钟</span>
                                    <span><i class="fas fa-users"></i> 50人</span>
                                </div>
                            </div>
                            <div class="venue-status">
                                <span class="status-badge completed">已结束</span>
                            </div>
                        </div>
                        
                        <div class="venue-content">
                            <div class="video-player" onclick="playVideo('digital-transformation')">
                                <div class="video-thumbnail">
                                    <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?w=300&h=200&fit=crop&crop=center" alt="数字化转型分会场">
                                    <div class="play-overlay">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                </div>
                                <div class="video-info">
                                    <h4>回顾视频</h4>
                                    <p>观看完整分会场内容</p>
                                </div>
                            </div>
                            
                            <div class="venue-details">
                                <h4>主要内容</h4>
                                <ul>
                                    <li>数字化转型战略规划</li>
                                    <li>技术架构设计与实施</li>
                                    <li>成功案例分析</li>
                                    <li>挑战与解决方案</li>
                                </ul>
                                
                                <div class="speaker-info">
                                    <strong>主讲人：</strong>
                                    <span>李总监 - 信息技术部</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分会场2 -->
                    <div class="venue-card">
                        <div class="venue-header">
                            <div class="venue-info">
                                <h3>智能管理分会场</h3>
                                <p>主题：AI技术在企业管理中的应用</p>
                                <div class="venue-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> B会议室</span>
                                    <span><i class="fas fa-clock"></i> 75分钟</span>
                                    <span><i class="fas fa-users"></i> 40人</span>
                                </div>
                            </div>
                            <div class="venue-status">
                                <span class="status-badge completed">已结束</span>
                            </div>
                        </div>
                        
                        <div class="venue-content">
                            <div class="video-player" onclick="playVideo('ai-management')">
                                <div class="video-thumbnail">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&crop=center" alt="智能管理分会场">
                                    <div class="play-overlay">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                </div>
                                <div class="video-info">
                                    <h4>回顾视频</h4>
                                    <p>观看完整分会场内容</p>
                                </div>
                            </div>
                            
                            <div class="venue-details">
                                <h4>主要内容</h4>
                                <ul>
                                    <li>AI技术在管理中的应用场景</li>
                                    <li>智能决策支持系统</li>
                                    <li>数据驱动的管理模式</li>
                                    <li>未来发展趋势</li>
                                </ul>
                                
                                <div class="speaker-info">
                                    <strong>主讲人：</strong>
                                    <span>王博士 - 技术研发中心</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分会场3 -->
                    <div class="venue-card">
                        <div class="venue-header">
                            <div class="venue-info">
                                <h3>团队协作分会场</h3>
                                <p>主题：高效团队协作与沟通机制</p>
                                <div class="venue-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> C会议室</span>
                                    <span><i class="fas fa-clock"></i> 60分钟</span>
                                    <span><i class="fas fa-users"></i> 35人</span>
                                </div>
                            </div>
                            <div class="venue-status">
                                <span class="status-badge live">进行中</span>
                            </div>
                        </div>
                        
                        <div class="venue-content">
                            <div class="video-player" onclick="playVideo('team-collaboration')">
                                <div class="video-thumbnail">
                                    <img src="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=300&h=200&fit=crop&crop=center" alt="团队协作分会场">
                                    <div class="play-overlay">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                    <div class="live-indicator">
                                        <span class="live-dot"></span>
                                        直播中
                                    </div>
                                </div>
                                <div class="video-info">
                                    <h4>实时直播</h4>
                                    <p>正在进行中，点击观看</p>
                                </div>
                            </div>
                            
                            <div class="venue-details">
                                <h4>主要内容</h4>
                                <ul>
                                    <li>团队协作工具与方法</li>
                                    <li>跨部门沟通机制</li>
                                    <li>项目管理最佳实践</li>
                                    <li>团队建设与激励</li>
                                </ul>
                                
                                <div class="speaker-info">
                                    <strong>主讲人：</strong>
                                    <span>张经理 - 人力资源部</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="venues-stats">
                    <div class="stat-item">
                        <i class="fas fa-video"></i>
                        <span>总分会场：<strong>3</strong>个</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>参与人数：<strong>125</strong>人</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>总时长：<strong>225</strong>分钟</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .venues-list {
            margin: 20px 0;
        }

        .venue-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4682B4;
        }

        .venue-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .venue-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .venue-info p {
            color: #666;
            font-size: 13px;
            margin-bottom: 10px;
        }

        .venue-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #666;
        }

        .venue-meta span {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .venue-meta i {
            color: #4682B4;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-badge.completed {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.live {
            background: #f8d7da;
            color: #721c24;
            animation: pulse 2s infinite;
        }

        .venue-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .video-player {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-player:hover {
            transform: translateY(-2px);
        }

        .video-thumbnail {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            height: 120px;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .play-overlay i {
            color: white;
            font-size: 20px;
        }

        .video-player:hover .play-overlay {
            background: rgba(70, 130, 180, 0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .live-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 71, 87, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .live-dot {
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }

        .video-info {
            margin-top: 10px;
        }

        .video-info h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 3px;
        }

        .video-info p {
            color: #666;
            font-size: 12px;
            margin: 0;
        }

        .venue-details h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .venue-details ul {
            list-style: none;
            padding: 0;
            margin: 0 0 15px 0;
        }

        .venue-details li {
            color: #666;
            font-size: 13px;
            padding: 5px 0;
            padding-left: 15px;
            position: relative;
        }

        .venue-details li:before {
            content: '•';
            color: #4682B4;
            position: absolute;
            left: 0;
        }

        .speaker-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
        }

        .speaker-info strong {
            color: #333;
        }

        .speaker-info span {
            color: #4682B4;
        }

        .venues-stats {
            display: flex;
            justify-content: space-around;
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 12px;
            text-align: center;
        }

        .stat-item i {
            color: #4682B4;
            font-size: 16px;
        }

        .stat-item strong {
            color: #333;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>

    <script>
        function goBack() {
            window.history.back();
        }

        function playVideo(venueId) {
            let message = '';
            switch(venueId) {
                case 'digital-transformation':
                    message = '正在播放：数字化转型分会场回顾\n（演示功能）\n在实际应用中会播放预录制的视频';
                    break;
                case 'ai-management':
                    message = '正在播放：智能管理分会场回顾\n（演示功能）\n在实际应用中会播放预录制的视频';
                    break;
                case 'team-collaboration':
                    message = '正在播放：团队协作分会场直播\n（演示功能）\n在实际应用中会播放实时直播或录制视频';
                    break;
            }
            alert(message);
        }
    </script>
</body>
</html>
