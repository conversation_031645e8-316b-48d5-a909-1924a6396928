# Stagewise AI辅助编辑工具 - 官方集成指南

![Stagewise Logo](https://stagewise.io/favicon.ico)

## 🚀 简介

Stagewise 是一款革命性的AI辅助前端编辑工具，允许您通过可视化的方式直接与AI代码助手交互，编辑您的Vue.js应用程序界面。

**官网:** [https://stagewise.io/](https://stagewise.io/)
**官方文档:** [https://stagewise.io/docs/quickstart](https://stagewise.io/docs/quickstart)

## ✨ 主要功能

- **可视化元素选择**: 直接点击页面元素进行选择和编辑
- **AI上下文感知**: 自动获取完整的DOM结构和组件信息
- **实时高亮显示**: 鼠标悬停时高亮显示可选择的元素
- **智能代码生成**: 与Cursor、VS Code等AI编辑器无缝集成
- **零配置使用**: 开箱即用，无需复杂设置

## 🛠️ 官方集成方式

### ✅ 已完成集成
我们已按照[官方文档](https://stagewise.io/docs/quickstart)的标准方式完成集成：

- [x] 安装官方Vue包：`@stagewise/toolbar-vue`
- [x] 安装Vue插件：`@stagewise-plugins/vue`
- [x] 在App.vue中集成组件
- [x] 开发环境自动激活
- [x] 生产环境自动隐藏

### 🔧 技术架构
```
前端项目/
├── package.json                 # 包含官方依赖包
├── src/
│   └── App.vue                  # 集成StagewiseToolbar组件
└── src/views/wel/index.vue      # 主页功能介绍
```

### 📦 安装的官方包
```bash
# Vue工具栏组件
@stagewise/toolbar-vue

# Vue插件，提供更好的AI行为支持
@stagewise-plugins/vue
```

## 📋 使用方法

### 1. 启动开发服务器
```bash
cd frontend
npm run dev
```

### 2. 访问应用
访问 `http://localhost:2888`，官方的stagewise工具栏将自动出现在页面上。

### 3. VS Code扩展集成（推荐）
为了获得完整的AI编辑体验，建议安装VS Code扩展：

1. 在VS Code中安装stagewise扩展
2. 使用快捷键 `CMD + Shift + P` (或 `Ctrl + Shift + P`)
3. 输入 `setupToolbar` 命令
4. 自动完成AI编辑器连接

## 🎯 核心优势

### 🔍 官方标准集成
- 使用官方维护的npm包
- 自动获得最新功能更新
- 与AI编辑器的完美兼容性

### 🎨 Vue专用优化
- 专为Vue.js优化的组件
- 支持Vue 3的Composition API
- 智能识别Vue组件结构

### ⚡ 零配置体验
- 开发环境自动启用
- 生产环境自动隐藏
- 无需手动配置

### 🔧 开发者友好
- TypeScript支持
- 完整的类型定义
- 详细的调试信息

## 🌐 浏览器兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 🔄 环境检测

### 开发环境 (工具栏激活)
- 使用 `import.meta.env.DEV` 自动检测
- Vite开发服务器环境

### 生产环境 (工具栏隐藏)
- 构建后的生产环境
- 自动排除工具栏代码

## 🌟 在主页中的介绍

访问系统主页，您会在折叠面板中看到详细的stagewise功能介绍，包括：
- 核心功能说明
- 使用方法指南
- 提示和注意事项
- 官网链接

## 🔗 相关链接

- **Stagewise官网**: [https://stagewise.io/](https://stagewise.io/)
- **官方快速入门**: [https://stagewise.io/docs/quickstart](https://stagewise.io/docs/quickstart)
- **VS Code扩展**: [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise)
- **Vue.js文档**: [https://vuejs.org/](https://vuejs.org/)
- **Element Plus**: [https://element-plus.org/](https://element-plus.org/)

## 🔧 集成代码示例

### App.vue中的集成
```vue
<template>
  <!-- Stagewise AI辅助编辑工具栏 (仅在开发环境显示) -->
  <StagewiseToolbar />
  
  <router-view />
</template>

<script setup>
import { StagewiseToolbar } from '@stagewise/toolbar-vue';
import { VuePlugin } from '@stagewise-plugins/vue';

// Stagewise配置 - 仅在开发环境启用
if (import.meta.env.DEV) {
  console.log('Stagewise: 开发环境检测到，正在初始化...');
}
</script>
```

### 安装命令
```bash
# 安装官方Vue包和插件
npm install -D @stagewise/toolbar-vue @stagewise-plugins/vue
```

## 🐛 故障排除

### 工具栏不显示
1. 确认运行在开发环境 (`npm run dev`)
2. 检查是否正确安装了官方包
3. 确认浏览器控制台没有错误信息

### 无法连接AI编辑器
1. 安装VS Code的stagewise扩展
2. 使用 `CMD + Shift + P` → `setupToolbar` 命令
3. 确认扩展已正确激活

### 控制台错误
1. 检查npm包版本是否为最新
2. 尝试重新安装：`npm install -D @stagewise/toolbar-vue`
3. 重新启动开发服务器

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看[官方文档](https://stagewise.io/docs/quickstart)
2. 检查浏览器控制台的错误信息
3. 访问[stagewise官网](https://stagewise.io/)获取最新文档
4. 联系开发团队获取技术支持

## 🎉 与自定义实现的对比

| 特性 | 官方集成 ✅ | 自定义实现 ❌ |
|------|------------|-------------|
| 维护性 | 官方维护，自动更新 | 需要手动维护 |
| 功能完整性 | 完整的AI编辑器集成 | 功能有限 |
| 兼容性 | 与所有AI编辑器兼容 | 仅基础功能 |
| 配置复杂度 | 零配置 | 需要复杂配置 |
| TypeScript支持 | 完整支持 | 无支持 |

---

**享受官方标准的AI辅助开发体验！** 🎉 