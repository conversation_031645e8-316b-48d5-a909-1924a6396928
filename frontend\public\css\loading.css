.loading {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #f4f7f9
}

.loading .loading-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    justify-content: center;
    align-items: center;
    flex-direction: column
}

.loading .dots {
    display: flex;
    padding: 98px;
    justify-content: center;
    align-items: center
}

.loading .loading-title {
    display: flex;
    font-weight: bold;
    margin-top: 30px;
    font-size: 18px;
    color: rgba(0, 0, 0, .85);
    justify-content: center;
    align-items: center
}


.dot {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 40px;
    margin-top: 30px;
    font-size: 28px;
    transform: rotate(45deg);
    box-sizing: border-box;
    animation: antRotate 1.2s infinite linear
}

.dot i {
    position: absolute;
    display: block;
    width: 20px;
    height: 20px;
    background-color: #0065cc;
    border-radius: 100%;
    opacity: .3;
    transform: scale(.75);
    animation: antSpinMove 1s infinite linear alternate;
    transform-origin: 50% 50%
}

.dot i:nth-child(1) {
    top: 0;
    left: 0
}

.dot i:nth-child(2) {
    top: 0;
    right: 0;
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.dot i:nth-child(3) {
    right: 0;
    bottom: 0;
    -webkit-animation-delay: .8s;
    animation-delay: .8s
}

.dot i:nth-child(4) {
    bottom: 0;
    left: 0;
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s
}

@keyframes antRotate {
    to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
    }
}

@-webkit-keyframes antRotate {
    to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
    }
}

@keyframes antSpinMove {
    to {
        opacity: 1
    }
}

@-webkit-keyframes antSpinMove {
    to {
        opacity: 1
    }
}
