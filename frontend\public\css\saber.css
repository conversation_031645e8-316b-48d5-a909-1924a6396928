html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
}

#loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    background-color: #151a26;
}

#loader-wrapper .loader-box {
    position: fixed;
    left: calc(50% - 250px);
    top: calc(50% - 100px);
    margin: 0 auto;
    width: 500px;
    height: 200px;
    text-align: center;
    vertical-align: center;
    font-weight: bold;
    color: #87888E;
    font-size: 35px;
}

#loader-wrapper .loader-box > span {
    opacity: 0.4;
    display: inline-block;
    animation: bouncingLoader 1s infinite alternate;
}

#loader-wrapper .loader-box > span:nth-child(2) {
    animation-delay: 0.1s;
}

#loader-wrapper .loader-box > span:nth-child(3) {
    animation-delay: 0.2s;
}

#loader-wrapper .loader-box > span:nth-child(4) {
    animation-delay: 0.3s;
}

#loader-wrapper .loader-box > span:nth-child(5) {
    animation-delay: 0.4s;
}

#loader-wrapper .loader-box > span:nth-child(6) {
    animation-delay: 0.5s;
}

#loader-wrapper .loader-box > span:nth-child(7) {
    animation-delay: 0.6s;
}

@keyframes bouncingLoader {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(25px);
    }
    100% {
        transform: translateY(0);
    }
}

#loader-wrapper .loader-title {
    font-weight: bold;
    z-index: 1002;
    position: absolute;
    top: 50%;
    margin-top: 15px;
    color: #87888E;
    font-size: 18px;
    width: 100%;
    height: 30px;
    text-align: center;
    opacity: 0.4;
    line-height: 30px;
}
