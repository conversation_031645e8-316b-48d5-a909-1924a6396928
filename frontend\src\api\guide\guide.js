import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hy/guide/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/hy/guide/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/hy/guide/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/hy/guide/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/hy/guide/submit',
    method: 'post',
    data: row
  })
}

