import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hy/liveStream/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/hy/liveStream/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/hy/liveStream/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/hy/liveStream/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/hy/liveStream/submit',
    method: 'post',
    data: row
  })
}

// 保存直播地址
export const saveLiveStreamUrl = (data) => {
  return request({
    url: '/hy/liveStream/saveUrl',
    method: 'post',
    data: data
  })
}

// 获取当前直播地址
export const getCurrentLiveUrl = () => {
  return request({
    url: '/hy/liveStream/getCurrentUrl',
    method: 'get'
  })
}

