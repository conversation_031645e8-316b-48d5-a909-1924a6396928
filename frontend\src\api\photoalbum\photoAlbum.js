import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hy/photoAlbum/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/hy/photoAlbum/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/hy/photoAlbum/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/hy/photoAlbum/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/hy/photoAlbum/submit',
    method: 'post',
    data: row
  })
}

