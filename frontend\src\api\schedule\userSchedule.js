import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hy/user-schedule/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/hy/user-schedule/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/hy/user-schedule/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/hy/user-schedule/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/hy/user-schedule/submit',
    method: 'post',
    data: row
  })
}

// 根据用户ID获取日程信息
export const getByUserId = (userId) => {
  return request({
    url: `/hy/user-schedule/by-user/${userId}`,
    method: 'get'
  })
}

// 获取当前用户的日程信息
export const getCurrentUserSchedule = () => {
  return request({
    url: '/hy/user-schedule/current',
    method: 'get'
  })
}

// 保存或更新当前用户的日程信息
export const saveCurrentUserSchedule = (row) => {
  return request({
    url: '/hy/user-schedule/save-current',
    method: 'post',
    data: row
  })
}
