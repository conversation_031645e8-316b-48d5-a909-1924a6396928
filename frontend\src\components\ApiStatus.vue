<template>
  <div class="api-status" v-if="showStatus">
    <div class="status-indicator" :class="statusClass" @click="toggleDetails">
      <i :class="statusIcon"></i>
      <span class="status-text">{{ statusText }}</span>
      <i class="fas fa-chevron-down toggle-icon" :class="{ 'rotated': showDetails }"></i>
    </div>
    
    <div class="status-details" v-if="showDetails">
      <div class="detail-item">
        <span class="label">响应时间:</span>
        <span class="value">{{ responseTime }}ms</span>
      </div>
      <div class="detail-item">
        <span class="label">数据来源:</span>
        <span class="value">{{ dataSource }}</span>
      </div>
      <div class="detail-item" v-if="errorMessage">
        <span class="label">错误信息:</span>
        <span class="value error">{{ errorMessage }}</span>
      </div>
      <div class="detail-actions">
        <button class="test-btn" @click="runTest" :disabled="testing">
          <i class="fas fa-vial" :class="{ 'fa-spin': testing }"></i>
          {{ testing ? '测试中...' : '测试API' }}
        </button>
        <button class="refresh-btn" @click="refreshData" :disabled="refreshing">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': refreshing }"></i>
          {{ refreshing ? '刷新中...' : '刷新数据' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { testApiConnectivity, monitorApiPerformance } from '@/utils/apiTester';

export default {
  name: 'ApiStatus',
  props: {
    // 是否显示状态指示器
    showStatus: {
      type: Boolean,
      default: true
    },
    // API调用函数
    apiCall: {
      type: Function,
      required: true
    },
    // API名称
    apiName: {
      type: String,
      required: true
    },
    // 数据来源
    dataSource: {
      type: String,
      default: 'unknown'
    },
    // 是否有错误
    hasError: {
      type: Boolean,
      default: false
    },
    // 错误信息
    errorMessage: {
      type: String,
      default: ''
    },
    // 响应时间
    responseTime: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showDetails: false,
      testing: false,
      refreshing: false,
      connectivity: true
    };
  },
  computed: {
    statusClass() {
      if (this.hasError) return 'status-error';
      if (this.dataSource === 'fallback') return 'status-warning';
      return 'status-success';
    },
    statusIcon() {
      if (this.hasError) return 'fas fa-exclamation-circle';
      if (this.dataSource === 'fallback') return 'fas fa-exclamation-triangle';
      return 'fas fa-check-circle';
    },
    statusText() {
      if (this.hasError) return 'API错误';
      if (this.dataSource === 'fallback') return '使用备用数据';
      return 'API正常';
    }
  },
  async mounted() {
    // 检查API连通性
    this.connectivity = await testApiConnectivity();
  },
  methods: {
    toggleDetails() {
      this.showDetails = !this.showDetails;
    },
    
    async runTest() {
      this.testing = true;
      
      try {
        const result = await monitorApiPerformance(this.apiCall, this.apiName);
        
        if (result.success) {
          this.$emit('test-success', result);
          if (this.$message) {
            this.$message.success(`${this.apiName} API测试成功`);
          }
        } else {
          this.$emit('test-error', result);
          if (this.$message) {
            this.$message.error(`${this.apiName} API测试失败: ${result.error}`);
          }
        }
      } catch (error) {
        this.$emit('test-error', { error: error.message });
        if (this.$message) {
          this.$message.error(`测试失败: ${error.message}`);
        }
      } finally {
        this.testing = false;
      }
    },
    
    async refreshData() {
      this.refreshing = true;
      
      try {
        this.$emit('refresh-data');
        
        // 等待一段时间让父组件完成刷新
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (this.$message) {
          this.$message.success('数据刷新完成');
        }
      } catch (error) {
        if (this.$message) {
          this.$message.error(`刷新失败: ${error.message}`);
        }
      } finally {
        this.refreshing = false;
      }
    }
  }
};
</script>

<style scoped>
.api-status {
  margin: 10px 0;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.status-success {
  background: rgba(46, 213, 115, 0.1);
  color: #2ed573;
  border: 1px solid rgba(46, 213, 115, 0.3);
}

.status-warning {
  background: rgba(255, 165, 2, 0.1);
  color: #ffa502;
  border: 1px solid rgba(255, 165, 2, 0.3);
}

.status-error {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.status-indicator:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-text {
  flex: 1;
  font-weight: 500;
}

.toggle-icon {
  font-size: 10px;
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.status-details {
  background: white;
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 12px;
  animation: slideDown 0.3s ease;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item:last-of-type {
  margin-bottom: 12px;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
}

.value.error {
  color: #ff4757;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.test-btn,
.refresh-btn {
  flex: 1;
  background: #4682B4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.test-btn:hover:not(:disabled),
.refresh-btn:hover:not(:disabled) {
  background: #1E90FF;
  transform: translateY(-1px);
}

.test-btn:disabled,
.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-status {
    font-size: 11px;
  }
  
  .status-indicator {
    padding: 6px 10px;
  }
  
  .status-details {
    padding: 10px;
  }
  
  .detail-actions {
    flex-direction: column;
  }
}
</style>
