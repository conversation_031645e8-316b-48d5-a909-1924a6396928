<template>
  <div class="error-container" v-if="show">
    <div class="error-message" :class="[`error-${type}`, { 'error-dismissible': dismissible }]">
      <div class="error-icon">
        <i :class="iconClass"></i>
      </div>
      <div class="error-content">
        <h4 v-if="title" class="error-title">{{ title }}</h4>
        <p class="error-text">{{ message }}</p>
        <div v-if="showRetry" class="error-actions">
          <button class="retry-btn" @click="handleRetry" :disabled="retrying">
            <i class="fas fa-redo" :class="{ 'fa-spin': retrying }"></i>
            {{ retrying ? '重试中...' : '重试' }}
          </button>
        </div>
      </div>
      <button v-if="dismissible" class="error-close" @click="handleClose">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorMessage',
  props: {
    // 是否显示错误信息
    show: {
      type: Boolean,
      default: false
    },
    // 错误类型
    type: {
      type: String,
      default: 'error', // error, warning, info
      validator: (value) => ['error', 'warning', 'info'].includes(value)
    },
    // 错误标题
    title: {
      type: String,
      default: ''
    },
    // 错误消息
    message: {
      type: String,
      required: true
    },
    // 是否可关闭
    dismissible: {
      type: Boolean,
      default: true
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: false
    },
    // 重试状态
    retrying: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconClass() {
      const iconMap = {
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      };
      return iconMap[this.type] || iconMap.error;
    }
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    handleRetry() {
      this.$emit('retry');
    }
  }
};
</script>

<style scoped>
.error-container {
  margin: 15px 0;
}

.error-message {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  animation: slideIn 0.3s ease-out;
}

/* 错误类型样式 */
.error-error {
  border-left-color: #ff4757;
  background: #fff5f5;
}

.error-error .error-icon {
  color: #ff4757;
}

.error-warning {
  border-left-color: #ffa502;
  background: #fffbf0;
}

.error-warning .error-icon {
  color: #ffa502;
}

.error-info {
  border-left-color: #4682B4;
  background: #f0f8ff;
}

.error-info .error-icon {
  color: #4682B4;
}

/* 图标样式 */
.error-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 内容样式 */
.error-content {
  flex: 1;
}

.error-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.error-text {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮 */
.error-actions {
  margin-top: 12px;
}

.retry-btn {
  background: #4682B4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.retry-btn:hover:not(:disabled) {
  background: #1E90FF;
  transform: translateY(-1px);
}

.retry-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* 关闭按钮 */
.error-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.error-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

.error-dismissible {
  padding-right: 40px;
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-message {
    padding: 12px;
  }
  
  .error-icon {
    font-size: 18px;
    margin-right: 10px;
  }
  
  .error-title {
    font-size: 15px;
  }
  
  .error-text {
    font-size: 13px;
  }
  
  .retry-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
