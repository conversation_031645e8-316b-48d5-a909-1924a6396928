<template>
  <div class="loading-container" v-if="show">
    <!-- 全屏加载遮罩 -->
    <div v-if="fullscreen" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">{{ text }}</p>
      </div>
    </div>
    
    <!-- 内联加载指示器 -->
    <div v-else class="loading-inline" :class="{ 'loading-small': size === 'small' }">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingIndicator',
  props: {
    // 是否显示加载指示器
    show: {
      type: Boolean,
      default: false
    },
    // 加载文本
    text: {
      type: String,
      default: '加载中...'
    },
    // 是否全屏显示
    fullscreen: {
      type: Boolean,
      default: false
    },
    // 尺寸大小
    size: {
      type: String,
      default: 'normal', // normal, small
      validator: (value) => ['normal', 'small'].includes(value)
    }
  }
};
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 全屏加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 200px;
}

/* 内联加载指示器 */
.loading-inline {
  padding: 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  margin: 20px 0;
}

.loading-inline.loading-small {
  padding: 10px;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
}

.loading-small .loading-spinner {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.spinner-ring {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 32px;
  height: 32px;
  margin: 4px;
  border: 3px solid #4682B4;
  border-radius: 50%;
  animation: spinner-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #4682B4 transparent transparent transparent;
}

.loading-small .spinner-ring {
  width: 20px;
  height: 20px;
  margin: 2px;
  border-width: 2px;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
}

@keyframes spinner-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 加载文本 */
.loading-text {
  color: #666;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.loading-small .loading-text {
  font-size: 12px;
}

.loading-overlay .loading-text {
  color: #333;
  font-size: 16px;
}

/* 骨架屏效果 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    padding: 20px;
    max-width: 150px;
  }
  
  .loading-text {
    font-size: 13px;
  }
}
</style>
