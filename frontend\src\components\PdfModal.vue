<template>
  <div v-if="visible" class="pdf-modal" @click="handleOverlayClick">
    <div class="pdf-modal-content" @click.stop>
      <div class="pdf-modal-header">
        <h3>{{ title }}</h3>
        <div class="pdf-modal-actions">
          <button @click="downloadPdf" class="pdf-action-btn">
            <i class="fas fa-download"></i>
            下载
          </button>
          <button @click="close" class="pdf-close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="pdf-modal-body">
        <PdfViewerCompat
          v-if="pdfUrl"
          :pdf-url="pdfUrl"
          :title="title"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PdfViewerCompat from './PdfViewerCompat.vue';

export default {
  name: 'PdfModal',
  components: {
    PdfViewerCompat
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: 'PDF预览'
    }
  },
  emits: ['close'],
  methods: {
    close() {
      this.$emit('close');
    },
    
    handleOverlayClick() {
      this.close();
    },
    
    downloadPdf() {
      if (this.pdfUrl) {
        window.open(this.pdfUrl, '_blank');
      }
    }
  },
  
  mounted() {
    // 监听ESC键关闭模态框
    const handleEsc = (e) => {
      if (e.key === 'Escape' && this.visible) {
        this.close();
      }
    };
    
    document.addEventListener('keydown', handleEsc);
    
    // 组件销毁时移除监听器
    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('keydown', handleEsc);
    });
  }
}
</script>

<style scoped>
.pdf-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.pdf-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  height: 90%;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease;
}

.pdf-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.pdf-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.pdf-modal-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pdf-action-btn,
.pdf-close-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.pdf-action-btn {
  color: #4682B4;
  border: 1px solid #4682B4;
}

.pdf-action-btn:hover {
  background: #4682B4;
  color: white;
}

.pdf-close-btn {
  color: #666;
  border: 1px solid #e0e0e0;
}

.pdf-close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.pdf-modal-body {
  flex: 1;
  overflow: hidden;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-modal-content {
    width: 95%;
    height: 95%;
    border-radius: 8px;
  }
  
  .pdf-modal-header {
    padding: 12px 15px;
  }
  
  .pdf-modal-header h3 {
    font-size: 16px;
  }
  
  .pdf-action-btn,
  .pdf-close-btn {
    padding: 6px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .pdf-modal-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .pdf-modal-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .pdf-modal-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
