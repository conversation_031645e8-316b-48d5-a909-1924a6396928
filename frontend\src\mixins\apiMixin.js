/**
 * API调用混入
 * 为Vue组件提供统一的API调用能力
 */

import { handleApiResponse, createLoadingManager, retryApiCall } from '@/utils/apiHelper';

export default {
  data() {
    return {
      // 加载状态管理器
      loadingManager: createLoadingManager(),
      // 组件级别的加载状态
      isLoading: false,
      // 错误状态
      hasError: false,
      errorMessage: ''
    };
  },

  computed: {
    // 获取当前组件的加载状态
    componentLoading() {
      return this.loadingManager.getLoading(this.$options.name || 'default');
    }
  },

  methods: {
    /**
     * 统一的API调用方法
     * @param {Function} apiCall - API调用函数
     * @param {Array|Object} fallbackData - 备用数据
     * @param {Object} options - 配置选项
     * @returns {Promise} 处理后的数据
     */
    async callApi(apiCall, fallbackData = [], options = {}) {
      const componentName = this.$options.name || 'default';
      
      // 设置加载状态
      this.setLoading(true);
      this.hasError = false;
      this.errorMessage = '';

      try {
        const result = await handleApiResponse(apiCall, fallbackData, {
          showError: true,
          errorMessage: `${componentName}数据获取失败，已加载默认数据`,
          ...options
        });

        // 记录数据来源
        if (result.source === 'fallback') {
          this.hasError = true;
          this.errorMessage = result.error || '数据获取失败';
        }

        return result.data;

      } catch (error) {
        console.error(`${componentName} API调用异常:`, error);
        this.hasError = true;
        this.errorMessage = error.message || '未知错误';
        return fallbackData;

      } finally {
        this.setLoading(false);
      }
    },

    /**
     * 带重试机制的API调用
     * @param {Function} apiCall - API调用函数
     * @param {Array|Object} fallbackData - 备用数据
     * @param {Object} options - 配置选项
     * @returns {Promise} 处理后的数据
     */
    async callApiWithRetry(apiCall, fallbackData = [], options = {}) {
      const { maxRetries = 2, retryDelay = 1000, ...apiOptions } = options;

      const retryApiCall = async () => {
        return await this.callApi(apiCall, fallbackData, apiOptions);
      };

      try {
        return await retryApiCall(retryApiCall, maxRetries, retryDelay);
      } catch (error) {
        console.error('API重试失败:', error);
        return fallbackData;
      }
    },

    /**
     * 设置加载状态
     * @param {Boolean} loading - 加载状态
     * @param {String} key - 状态键名
     */
    setLoading(loading = true, key = null) {
      const componentName = key || this.$options.name || 'default';
      this.loadingManager.setLoading(componentName, loading);
      this.isLoading = loading;
    },

    /**
     * 获取加载状态
     * @param {String} key - 状态键名
     * @returns {Boolean} 加载状态
     */
    getLoading(key = null) {
      const componentName = key || this.$options.name || 'default';
      return this.loadingManager.getLoading(componentName);
    },

    /**
     * 刷新数据
     * 子组件需要实现此方法
     */
    async refreshData() {
      console.warn(`${this.$options.name || 'Component'} 需要实现 refreshData 方法`);
    },

    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {String} context - 错误上下文
     */
    handleApiError(error, context = '') {
      console.error(`API错误 ${context}:`, error);
      this.hasError = true;
      this.errorMessage = error.message || '操作失败';
      
      // 显示错误提示
      if (this.$message) {
        this.$message.error(`${context} ${this.errorMessage}`);
      }
    },

    /**
     * 清除错误状态
     */
    clearError() {
      this.hasError = false;
      this.errorMessage = '';
    },

    /**
     * 格式化API响应数据
     * @param {*} data - 原始数据
     * @param {String} type - 数据类型
     * @returns {*} 格式化后的数据
     */
    formatApiData(data, type = 'default') {
      // 子组件可以重写此方法来自定义数据格式化
      return data;
    },

    /**
     * 验证API响应数据
     * @param {*} data - 数据
     * @param {String} type - 数据类型
     * @returns {Boolean} 验证结果
     */
    validateApiData(data, type = 'default') {
      // 基础验证
      if (data === null || data === undefined) {
        return false;
      }

      // 数组类型验证
      if (type === 'array' || type === 'list') {
        return Array.isArray(data);
      }

      // 对象类型验证
      if (type === 'object') {
        return typeof data === 'object' && !Array.isArray(data);
      }

      return true;
    },

    /**
     * 获取分页数据
     * @param {Function} apiCall - API调用函数
     * @param {Number} current - 当前页
     * @param {Number} size - 页面大小
     * @param {Object} params - 查询参数
     * @param {Array} fallbackData - 备用数据
     * @returns {Promise} 分页数据
     */
    async getPaginatedData(apiCall, current = 1, size = 10, params = {}, fallbackData = []) {
      const apiCallWithParams = () => apiCall(current, size, params);
      
      return await this.callApi(apiCallWithParams, fallbackData, {
        dataPath: 'data.data.records',
        transform: (data) => this.formatApiData(data, 'array'),
        validate: (data) => this.validateApiData(data, 'array')
      });
    },

    /**
     * 获取详情数据
     * @param {Function} apiCall - API调用函数
     * @param {String|Number} id - 数据ID
     * @param {Object} fallbackData - 备用数据
     * @returns {Promise} 详情数据
     */
    async getDetailData(apiCall, id, fallbackData = {}) {
      const apiCallWithId = () => apiCall(id);
      
      return await this.callApi(apiCallWithId, fallbackData, {
        dataPath: 'data.data',
        transform: (data) => this.formatApiData(data, 'object'),
        validate: (data) => this.validateApiData(data, 'object')
      });
    },

    /**
     * 提交数据
     * @param {Function} apiCall - API调用函数
     * @param {Object} data - 提交的数据
     * @returns {Promise} 提交结果
     */
    async submitData(apiCall, data) {
      this.setLoading(true, 'submit');
      
      try {
        const result = await apiCall(data);
        
        if (this.$message) {
          this.$message.success('操作成功');
        }
        
        return result;
        
      } catch (error) {
        this.handleApiError(error, '提交失败:');
        throw error;
        
      } finally {
        this.setLoading(false, 'submit');
      }
    }
  },

  // 组件销毁时清理
  beforeUnmount() {
    if (this.loadingManager) {
      this.loadingManager.clearLoading(this.$options.name || 'default');
    }
  }
};
