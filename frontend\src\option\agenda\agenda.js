export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
        label: "会议日期",
        prop: "date",
        type: "date",
        format: "YYYY-MM-DD",
        valueFormat: "YYYY-MM-DD",
        search: true,
    },
    {
      label: "开始时间",
      prop: "startTime",
      type: "time",
      format: "HH:mm:ss",
      valueFormat: "HH:mm:ss",
    },
    {
      label: "结束时间",
      prop: "endTime",
      type: "time",
      format: "HH:mm:ss",
      valueFormat: "HH:mm:ss",
    },
    {
      label: "议题/主题",
      prop: "topic",
      type: "input",
      search: true,
    },
    {
      label: "演讲人",
      prop: "speaker",
      type: "input",
      search: true,
    },
    {
      label: "会场",
      prop: "venue",
      type: "input",
    },
    {
      label: "议程描述",
      prop: "description",
      type: 'textarea',

    },
    {
      label: "",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    
    {
      label: "",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
