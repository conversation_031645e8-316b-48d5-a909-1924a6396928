export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "直播标题",
      prop: "title",
      type: "input",
    },
    {
      label: "主讲人",
      prop: "speaker",
      type: "input",
    },
    {
      label: "直播地址",
      prop: "url",
      type: "input",
      span: 24,
      hide: false,
    },
    {
      label: "视频文件上传",
      prop: "videoFile",
      type: "upload",
      drag: true,
      loadText: '视频上传中，请稍等',
      span: 24,
      propsHttp: {
        res: 'data',
        url: 'link'
      },
      tip: '请上传 .mp4,.avi,.mov,.wmv 等视频格式文件，上传成功后会自动填充到直播地址',
      action: '/blade-resource/oss/endpoint/put-file',
      accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      tableDisplay: false,
    },
    {
      label: "开始时间",
      prop: "startTime",
      type: "date",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
    },
    {
      label: "结束时间",
      prop: "endTime",
      type: "date",
      format: "YYYY-MM-DD HH:mm:ss",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
    },
    {
      label: "直播状态",
      prop: "statusText",
      type: "input",
    },
    {
      label: "直播描述",
      prop: "description",
      type: "input",
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
