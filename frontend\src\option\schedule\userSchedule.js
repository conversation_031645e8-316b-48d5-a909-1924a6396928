export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用户",
      prop: "userId",
      type: "select",
      dicUrl: '/blade-system/user/user-list',
      props: {
        label: 'realName',
        value: 'id'
      },
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择用户',
          trigger: 'change',
        },
      ],
    },
    {
      label: "用户姓名",
      prop: "userRealName",
      type: "input",
      search: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },

    {
      label: "日程信息",
      prop: "scheduleContent",
      type: "textarea",
      span: 24,
      minRows: 6,
      rules: [
        {
          required: true,
          message: '请输入日程信息',
          trigger: 'blur',
        },
      ],
    },
    {
      label: "用餐信息",
      prop: "diningInfo",
      type: "textarea",
      span: 24,
      minRows: 4,
      tip: '请输入JSON格式的用餐信息',
    },
    {
      label: "住宿信息",
      prop: "accommodationInfo",
      type: "textarea",
      span: 24,
      minRows: 4,
      tip: '请输入JSON格式的住宿信息',
    },

    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
