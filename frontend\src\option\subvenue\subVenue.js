export default {
  height:'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  selection: true,
  dialogClickModal: false,
  column: [
    {
      label: "主键，自增",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "分会场名称",
      prop: "name",
      type: "input",
      search: true,
    },
    {
      label: "地点",
      prop: "location",
      type: "input",
    },
    {
      label: "关联议程",
      prop: "hyAgendaId",
      type: "input",
    },
    {
      label: "负责人",
      prop: "manager",
      type: "input",
    },
    {
      label: "描述",
      prop: "description",
      type: "textarea",
      span: 24,
    },
    {
      label: "视频文件",
      prop: "videoUrl",
      type: "upload",
      span: 24,
      listType: "text",
      action: "/blade-resource/oss/endpoint/put-file",
      tip: "只能上传mp4/avi/mov等视频格式文件，且不超过200MB",
      accept: ".mp4,.avi,.mov,.wmv,.flv,.mkv",
      multiple: false,
      limit: 1,
      propsHttp: {
        res: 'data',
        url: 'link'
      },
      tableDisplay: false,
    },
    {
      label: "PDF文档",
      prop: "pdfUrl",
      type: "upload",
      span: 24,
      listType: "text",
      action: "/blade-resource/oss/endpoint/put-file",
      tip: "只能上传PDF格式文件，且不超过50MB",
      accept: ".pdf",
      multiple: false,
      limit: 1,
      propsHttp: {
        res: 'data',
        url: 'link'
      },
      tableDisplay: false,
    },
    {
      label: "创建人",
      prop: "createUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建部门",
      prop: "createDept",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "是否删除",
      prop: "isDeleted",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
  ]
}
