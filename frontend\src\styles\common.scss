// 全局变量
@import './variables.scss';

a {
  text-decoration: none;
  color: #333;
}

* {
  outline: none;
}

.avue-sidebar,
.avue-top,
.avue-logo,
.avue-layout
.login-logo,
.avue-main {
  transition: all .3s;
}

.avue-layout {
  display: flex;
  height: 100%;
  overflow: hidden;

  &--horizontal {
    flex-direction: column;

    .avue-sidebar {
      width: 100%;
      height: $top_height;
      display: flex;

      .avue-menu, .el-menu-item, .el-sub-menu__title {
        height: $top_height;
        line-height: $top_height;
      }

      .is-active:before {
        display: none;
      }
    }

    .avue-logo {
      width: $sidebar_width
    }
  }
}

.avue-contail {
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  background-size: 100%;
  background-repeat: no-repeat;
}

.avue--collapse {
  .avue-sidebar,
  .avue-logo {
    width: $sidebar_collapse;
  }
}

.avue-main {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  background: #f0f2f5;
}

#avue-view {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 10px;
}

.avue-view {
  width: 100%;
  box-sizing: border-box;
}

.avue-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;

  .copyright {
    color: #666;
    font-size: 12px;
  }
}

.mac_bg {
  background-image: url("/img/bg.jpg");
  background-color: #000;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

// ele样式覆盖
@import './element-ui.scss';
// 顶部右侧显示
@import './top.scss';
// 导航标签
@import './tags.scss';
// 工具类函数
@import './mixin.scss';
// 侧面导航栏
@import './sidebar.scss';
//主题
@import './theme/index.scss';
//通用配置
@import './normalize.scss';
//图标配置
@import './iconfont.scss';
//登录样式
@import "./login.scss";
//适配
@import './media.scss';
//滚动条样式
@include scrollBar;
