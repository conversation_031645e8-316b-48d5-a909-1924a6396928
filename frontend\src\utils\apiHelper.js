/**
 * API数据处理工具类
 * 统一处理API响应数据、错误处理和fallback机制
 */

/**
 * 统一的API响应数据处理
 * @param {Promise} apiCall - API调用Promise
 * @param {Array|Object} fallbackData - 备用数据
 * @param {Object} options - 配置选项
 * @returns {Promise} 处理后的数据
 */
export const handleApiResponse = async (apiCall, fallbackData = [], options = {}) => {
  const {
    dataPath = 'data.data.records', // 数据路径，支持嵌套访问
    showError = true, // 是否显示错误信息
    errorMessage = '数据获取失败，已加载默认数据', // 自定义错误信息
    transform = null, // 数据转换函数
    validate = null, // 数据验证函数
    timeout = 10000 // 请求超时时间
  } = options;

  try {
    // 设置超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout);
    });

    // 执行API调用
    const response = await Promise.race([apiCall, timeoutPromise]);
    
    // 检查响应状态
    if (!response || !response.data) {
      throw new Error('响应数据格式错误');
    }

    // 根据数据路径提取数据
    let data = getNestedValue(response, dataPath);
    
    // 数据验证
    if (validate && !validate(data)) {
      throw new Error('数据验证失败');
    }

    // 检查数据是否为空
    if (!data || (Array.isArray(data) && data.length === 0)) {
      console.warn('API返回空数据，使用fallback数据');
      data = fallbackData;
    }

    // 数据转换
    if (transform && typeof transform === 'function') {
      data = transform(data);
    }

    return {
      success: true,
      data: data,
      source: 'api'
    };

  } catch (error) {
    console.error('API调用失败:', error.message);
    
    // 显示错误信息（可选）
    if (showError && window.ElMessage) {
      window.ElMessage({
        message: errorMessage,
        type: 'warning',
        duration: 3000
      });
    }

    // 返回fallback数据
    let data = fallbackData;
    if (transform && typeof transform === 'function') {
      data = transform(data);
    }

    return {
      success: false,
      data: data,
      source: 'fallback',
      error: error.message
    };
  }
};

/**
 * 根据路径获取嵌套对象的值
 * @param {Object} obj - 目标对象
 * @param {String} path - 属性路径，如 'data.data.records'
 * @returns {*} 提取的值
 */
export const getNestedValue = (obj, path) => {
  if (!path) return obj;
  
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
};

/**
 * 数据格式转换器
 */
export const dataTransformers = {
  // 议程数据转换
  agenda: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map(item => ({
      id: item.id || Date.now(),
      time: item.startTime && item.endTime ? `${item.startTime}-${item.endTime}` : (item.time || ''),
      topic: item.topic || item.title || item.name || '',
      speaker: item.speaker || item.presenter || '',
      venue: item.venue || item.location || '',
      description: item.description || item.content || '',
      date: item.date || ''
    }));
  },

  // 直播数据转换
  liveStream: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map(item => ({
      id: item.id || Date.now(),
      url: item.url || item.streamUrl || '',
      speaker:item.speaker||'',
      title: item.title || item.name || '',
      status: item.status || 'offline',
      statusText: item.statusText || item.status_text || '',
      viewerCount: item.viewerCount || item.viewer_count || 0,
      startTime: item.startTime || item.start_time || '',
      endTime: item.endTime || item.end_time || '',
      description: item.description || item.content || ''
    }));
  },

  // 资料数据转换
  materials: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map(item => ({
      id: item.id || Date.now(),
      title: item.title || item.name || '',
      description: item.description || item.content || '',
      url: item.url || item.fileUrl || item.downloadUrl || '',
      size: item.size || item.fileSize || '',
      category: item.category||item.type || 'document',
      date: item.uploadTime || item.createTime || new Date().toISOString().split('T')[0]
    }));
  },

  // 照片数据转换
  photos: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map(item => ({
      id: item.id || Date.now(),
      url: item.url || item.imageUrl || item.image_url || '',
      title: item.title || item.name || '',
      description: item.description || item.content || '',
      category: item.category || 'all',
      date: item.date || item.uploadTime || item.createTime || new Date().toISOString().split('T')[0],
      likes: item.likes || 0,
      liked: item.liked || false,
      uploader: item.uploader || item.upload_user || ''
    }));
  },

  // 指南数据转换
  guides: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map(item => ({
      id: item.id || Date.now(),
      title: item.title || item.name || '',
      description: item.description || item.content || '',
      category: item.category || 'all',
      icon: item.icon || 'fas fa-info-circle',
      details: item.details || [],
      actions: item.actions || []
    }));
  },

  // 分会场数据转换
  subVenues: (data) => {
    // 处理API响应格式: { data: { records: [...] } }
    let records = data;
    if (data && data.data && data.data.records) {
      records = data.data.records;
    } else if (data && data.records) {
      records = data.records;
    }

    if (!Array.isArray(records)) return [];

    return records.map((item, index) => ({
      id: item.id || Date.now() + index,
      number: item.number || String.fromCharCode(68 + index), // D, E, F...
      name: item.name || item.title || `分会场${String.fromCharCode(68 + index)}`,
      location: item.location || item.address || `会议中心${String.fromCharCode(68 + index)}厅`,
      time: item.time || item.startTime && item.endTime ? `${item.startTime}-${item.endTime}` : '待定',
      capacity: parseInt(item.capacity) || 100,
      currentAttendees: parseInt(item.currentAttendees) || parseInt(item.attendees) || Math.floor((parseInt(item.capacity) || 100) * 0.8),
      status: item.status || 'active',
      currentTopic: item.currentTopic || item.topic || item.title || item.description || '会议议题',
      speaker: item.speaker || item.presenter || item.manager || '专家',
      speakerTitle: item.speakerTitle || item.presenterTitle || item.position || '行业专家',
      // 视频和PDF URL字段
      videoUrl: item.videoUrl || '',
      pdfUrl: item.pdfUrl || '',
      // 保留原有字段以兼容旧版本
      manager: item.manager || item.contact || '',
      phone: item.phone || item.contactPhone || '',
      equipment: item.equipment || item.facilities || '',
      schedule: item.schedule || [],
      description: item.description || item.content || ''
    }));
  }
};

/**
 * 数据验证器
 */
export const dataValidators = {
  // 验证数组数据
  isValidArray: (data) => Array.isArray(data),
  
  // 验证对象数据
  isValidObject: (data) => data && typeof data === 'object' && !Array.isArray(data),
  
  // 验证非空数据
  isNotEmpty: (data) => {
    if (Array.isArray(data)) return data.length > 0;
    if (typeof data === 'object') return Object.keys(data).length > 0;
    return data !== null && data !== undefined && data !== '';
  }
};

/**
 * 创建加载状态管理器
 */
export const createLoadingManager = () => {
  const loadingStates = new Map();

  return {
    // 设置加载状态
    setLoading: (key, loading = true) => {
      loadingStates.set(key, loading);
    },

    // 获取加载状态
    getLoading: (key) => {
      return loadingStates.get(key) || false;
    },

    // 清除加载状态
    clearLoading: (key) => {
      loadingStates.delete(key);
    },

    // 获取所有加载状态
    getAllLoading: () => {
      return Object.fromEntries(loadingStates);
    }
  };
};

/**
 * API调用重试机制
 * @param {Function} apiCall - API调用函数
 * @param {Number} maxRetries - 最大重试次数
 * @param {Number} delay - 重试延迟时间（毫秒）
 * @returns {Promise} API调用结果
 */
export const retryApiCall = async (apiCall, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw lastError;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
};

export default {
  handleApiResponse,
  getNestedValue,
  dataTransformers,
  dataValidators,
  createLoadingManager,
  retryApiCall
};
