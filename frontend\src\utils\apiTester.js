/**
 * API测试工具
 * 用于测试和验证所有API集成
 */

import { getList as getAgendaList } from '@/api/agenda/agenda';
import { getList as getLiveStreamList } from '@/api/livestream/liveStream';
import { getList as getMaterialsList } from '@/api/materials/materials';
import { getList as getPhotoList } from '@/api/photoalbum/photoAlbum';
import { getList as getGuideList } from '@/api/guide/guide';
import { getList as getSubVenueList } from '@/api/subvenue/subVenue';

/**
 * API测试配置
 */
const API_TEST_CONFIG = {
  agenda: {
    name: '会议议程',
    api: getAgendaList,
    endpoint: '/hy/agenda/list',
    expectedFields: ['id', 'time', 'topic', 'speaker', 'venue']
  },
  liveStream: {
    name: '云直播',
    api: getLiveStreamList,
    endpoint: '/hy/liveStream/list',
    expectedFields: ['id', 'url', 'title', 'status']
  },
  materials: {
    name: '会议资料',
    api: getMaterialsList,
    endpoint: '/hy/materials/list',
    expectedFields: ['id', 'title', 'url', 'type', 'size']
  },
  photos: {
    name: '在线相册',
    api: getPhotoList,
    endpoint: '/hy/photoalbum/list',
    expectedFields: ['id', 'url', 'title', 'category']
  },
  guides: {
    name: '参会指南',
    api: getGuideList,
    endpoint: '/hy/guide/list',
    expectedFields: ['id', 'title', 'content', 'category']
  },
  subVenues: {
    name: '分会场信息',
    api: getSubVenueList,
    endpoint: '/hy/subvenue/list',
    expectedFields: ['id', 'name', 'location', 'capacity']
  }
};

/**
 * 测试单个API
 * @param {String} apiKey - API配置键名
 * @param {Object} config - API配置
 * @returns {Promise<Object>} 测试结果
 */
async function testSingleApi(apiKey, config) {
  const startTime = Date.now();
  const result = {
    name: config.name,
    endpoint: config.endpoint,
    success: false,
    responseTime: 0,
    dataCount: 0,
    hasValidData: false,
    missingFields: [],
    error: null,
    response: null
  };

  try {
    console.log(`🧪 测试 ${config.name} API...`);
    
    // 调用API
    const response = await config.api(1, 10, {});
    result.responseTime = Date.now() - startTime;
    result.response = response;

    // 检查响应结构
    if (!response || !response.data) {
      throw new Error('响应数据格式错误：缺少data字段');
    }

    // 检查数据结构
    const data = response.data.data?.records || response.data.records || response.data;
    
    if (Array.isArray(data)) {
      result.dataCount = data.length;
      result.hasValidData = data.length > 0;

      // 验证数据字段
      if (data.length > 0) {
        const firstItem = data[0];
        const missingFields = config.expectedFields.filter(field => 
          !(field in firstItem)
        );
        result.missingFields = missingFields;
      }
    } else {
      result.hasValidData = data !== null && data !== undefined;
    }

    result.success = true;
    console.log(`✅ ${config.name} API测试成功`);

  } catch (error) {
    result.error = error.message;
    result.responseTime = Date.now() - startTime;
    console.error(`❌ ${config.name} API测试失败:`, error.message);
  }

  return result;
}

/**
 * 测试所有API
 * @returns {Promise<Object>} 所有API的测试结果
 */
export async function testAllApis() {
  console.log('🚀 开始API集成测试...');
  
  const results = {};
  const summary = {
    total: 0,
    success: 0,
    failed: 0,
    avgResponseTime: 0,
    totalDataCount: 0
  };

  // 并发测试所有API
  const testPromises = Object.entries(API_TEST_CONFIG).map(async ([key, config]) => {
    const result = await testSingleApi(key, config);
    results[key] = result;
    return result;
  });

  const testResults = await Promise.all(testPromises);

  // 计算汇总信息
  summary.total = testResults.length;
  summary.success = testResults.filter(r => r.success).length;
  summary.failed = summary.total - summary.success;
  summary.avgResponseTime = Math.round(
    testResults.reduce((sum, r) => sum + r.responseTime, 0) / summary.total
  );
  summary.totalDataCount = testResults.reduce((sum, r) => sum + r.dataCount, 0);

  // 输出测试报告
  console.log('\n📊 API测试报告:');
  console.log(`总计: ${summary.total} 个API`);
  console.log(`成功: ${summary.success} 个`);
  console.log(`失败: ${summary.failed} 个`);
  console.log(`平均响应时间: ${summary.avgResponseTime}ms`);
  console.log(`总数据条数: ${summary.totalDataCount} 条`);

  return {
    results,
    summary,
    timestamp: new Date().toISOString()
  };
}

/**
 * 测试API连通性
 * @returns {Promise<Boolean>} 连通性测试结果
 */
export async function testApiConnectivity() {
  try {
    // 测试一个简单的API调用
    const response = await getAgendaList(1, 1, {});
    return true;
  } catch (error) {
    console.error('API连通性测试失败:', error.message);
    return false;
  }
}

/**
 * 生成API测试报告
 * @param {Object} testResults - 测试结果
 * @returns {String} HTML格式的测试报告
 */
export function generateTestReport(testResults) {
  const { results, summary, timestamp } = testResults;
  
  let html = `
    <div class="api-test-report">
      <h2>API集成测试报告</h2>
      <p>测试时间: ${new Date(timestamp).toLocaleString()}</p>
      
      <div class="summary">
        <h3>测试汇总</h3>
        <ul>
          <li>总计: ${summary.total} 个API</li>
          <li>成功: ${summary.success} 个</li>
          <li>失败: ${summary.failed} 个</li>
          <li>平均响应时间: ${summary.avgResponseTime}ms</li>
          <li>总数据条数: ${summary.totalDataCount} 条</li>
        </ul>
      </div>
      
      <div class="details">
        <h3>详细结果</h3>
        <table>
          <thead>
            <tr>
              <th>API名称</th>
              <th>状态</th>
              <th>响应时间</th>
              <th>数据条数</th>
              <th>错误信息</th>
            </tr>
          </thead>
          <tbody>
  `;

  Object.values(results).forEach(result => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    const error = result.error || '-';
    
    html += `
      <tr>
        <td>${result.name}</td>
        <td>${status}</td>
        <td>${result.responseTime}ms</td>
        <td>${result.dataCount}</td>
        <td>${error}</td>
      </tr>
    `;
  });

  html += `
          </tbody>
        </table>
      </div>
    </div>
  `;

  return html;
}

/**
 * 监控API性能
 * @param {Function} apiCall - API调用函数
 * @param {String} apiName - API名称
 * @returns {Promise<Object>} 性能监控结果
 */
export async function monitorApiPerformance(apiCall, apiName) {
  const startTime = performance.now();
  const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  try {
    const result = await apiCall();
    const endTime = performance.now();
    const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
    
    return {
      success: true,
      apiName,
      responseTime: Math.round(endTime - startTime),
      memoryUsage: endMemory - startMemory,
      timestamp: new Date().toISOString(),
      result
    };
  } catch (error) {
    const endTime = performance.now();
    
    return {
      success: false,
      apiName,
      responseTime: Math.round(endTime - startTime),
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

export default {
  testAllApis,
  testApiConnectivity,
  generateTestReport,
  monitorApiPerformance
};
