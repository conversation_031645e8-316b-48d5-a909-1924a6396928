<template>
  <basic-container>
    <avue-crud :option="option"
               v-model:search="search"
               v-model:page="page"
               v-model="form"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               :upload-before="uploadBefore"
               :upload-after="uploadAfter"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.subVenue_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   @click="handleExport">导 出
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/subvenue/subVenue";
  import option from "@/option/subvenue/subVenue";
  import {mapGetters} from "vuex";
  import {exportBlob} from "@/api/common";
  import {getToken} from '@/utils/auth';
  import {downloadXls} from "@/utils/util";
  import {dateNow} from "@/utils/date";
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: option,
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.subVenue_add, false),
          viewBtn: this.validData(this.permission.subVenue_view, false),
          delBtn: this.validData(this.permission.subVenue_delete, false),
          editBtn: this.validData(this.permission.subVenue_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        // 处理上传组件返回的数组格式，转换为字符串
        const processedRow = this.processUploadData(row);
        add(processedRow).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, done, loading) {
        // 处理上传组件返回的数组格式，转换为字符串
        const processedRow = this.processUploadData(row);
        update(processedRow).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          loading();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleExport() {
        let downloadUrl = `/hy/subVenue/export-subVenue?${this.website.tokenHeader}=${getToken()}`;
        const {
            name,
        } = this.query;
        let values = {
            name_like: name,
        };
        this.$confirm("是否导出数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          NProgress.start();
          exportBlob(downloadUrl, values).then(res => {
            downloadXls(res.data, `分会场信息表${dateNow()}.xlsx`);
            NProgress.done();
          })
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // 处理编辑时的数据回显，将字符串转换为数组格式供上传组件使用
            this.form = this.processDisplayData(this.form);
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;

        const {
          name,
        } = this.query;

        let values = {
          name_like: name,
        };

        getList(page.currentPage, page.pageSize, values).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      /**
       * 文件上传前的处理
       */
      uploadBefore(file, done, loading, column) {
        // 检查文件类型
        if (column.prop === 'videoUrl') {
          const isVideo = file.type.startsWith('video/');
          if (!isVideo) {
            this.$message.error('只能上传视频文件！');
            loading();
            return false;
          }
          // 检查视频文件大小（限制为100MB）
          const isLt200M = file.size / 1024 / 1024 < 200;
          if (!isLt200M) {
            this.$message.error('上传视频大小不能超过 200MB!');
            loading();
            return false;
          }
          this.$message.success('开始上传视频文件...');
        } else if (column.prop === 'pdfUrl') {
          const isPdf = file.type === 'application/pdf';
          if (!isPdf) {
            this.$message.error('只能上传PDF文件！');
            loading();
            return false;
          }
          // 检查PDF文件大小（限制为50MB）
          const isLt50M = file.size / 1024 / 1024 < 50;
          if (!isLt50M) {
            this.$message.error('上传PDF大小不能超过 50MB!');
            loading();
            return false;
          }
          this.$message.success('开始上传PDF文件...');
        }
        done();
      },
      /**
       * 文件上传成功后的处理
       */
      // uploadAfter(res, done, loading, column) {
      //   if (res && res.success && res.data) {
      //     // 获取上传成功后的文件链接
      //     const fileUrl = res.data.link;

      //     if (column.prop === 'videoUrl') {
      //       this.$message.success('视频文件上传成功！');
      //     } else if (column.prop === 'pdfUrl') {
      //       this.$message.success('PDF文件上传成功！');
      //     }
      //     done();
      //   } else {
      //     this.$message.error('文件上传失败，请重试！');
      //     loading();
      //   }
      // },
      /**
       * 处理上传组件返回的数组格式数据，转换为字符串
       */
      processUploadData(row) {
        const processedRow = { ...row };

        // 处理videoUrl字段
        if (processedRow.videoUrl && Array.isArray(processedRow.videoUrl)) {
          processedRow.videoUrl = processedRow.videoUrl.length > 0 ? processedRow.videoUrl[0] : '';
        }

        // 处理pdfUrl字段
        if (processedRow.pdfUrl && Array.isArray(processedRow.pdfUrl)) {
          processedRow.pdfUrl = processedRow.pdfUrl.length > 0 ? processedRow.pdfUrl[0] : '';
        }

        return processedRow;
      },
      /**
       * 处理编辑时的数据回显，将字符串转换为数组格式供上传组件使用
       */
      processDisplayData(row) {
        const processedRow = { ...row };

        // 处理videoUrl字段 - 将字符串转换为数组格式
        if (processedRow.videoUrl && typeof processedRow.videoUrl === 'string' && processedRow.videoUrl.trim() !== '') {
          processedRow.videoUrl = [processedRow.videoUrl];
        }

        // 处理pdfUrl字段 - 将字符串转换为数组格式
        if (processedRow.pdfUrl && typeof processedRow.pdfUrl === 'string' && processedRow.pdfUrl.trim() !== '') {
          processedRow.pdfUrl = [processedRow.pdfUrl];
        }

        return processedRow;
      }
    }
  };
</script>

<style>
</style>
