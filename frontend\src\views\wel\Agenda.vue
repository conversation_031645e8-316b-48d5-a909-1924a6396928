<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-calendar-alt"></i>
          <h2>{{mainTitle||'会议议程'}}</h2>
          <p>{{subTitle||'数智攀登，管理跃升 - 企业管理现场会详细安排'}}</p>
        </div>

        <!-- 加载状态 -->
        <LoadingIndicator
          :show="isLoading"
          text="正在加载议程数据..."
          size="normal"
        />

        <!-- 错误提示 -->
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          title="数据加载提示"
          :message="errorMessage"
          :show-retry="true"
          :retrying="isLoading"
          @retry="refreshData"
          @close="clearError"
        />

        <!-- API状态监控需要可以打开调试 -->
        <!-- <ApiStatus
          :show-status="true"
          :api-call="getTestApiCall"
          api-name="会议议程"
          :data-source="dataSource"
          :has-error="hasError"
          :error-message="errorMessage"
          :response-time="responseTime"
          @test-success="handleTestSuccess"
          @test-error="handleTestError"
          @refresh-data="refreshData"
        /> -->

        <!-- 动态渲染议程数据 -->
        <div v-if="agendaList && agendaList.length > 0">
          <!-- 按日期分组显示 -->
          <div v-for="(dayAgenda, date) in groupedAgenda" :key="date" class="day-section">
            <h3 class="day-title">
              <i class="fas fa-calendar-day"></i>
              {{ formatDate(date) }}
            </h3>

            <div v-for="item in dayAgenda" :key="item.id" class="list-item">
              <div class="time">{{ item.time }}</div>
              <h3>{{ item.topic }}</h3>
              <p v-if="item.description">{{ item.description }}</p>
              <p v-if="item.speaker"><i class="fas fa-user"></i> {{ item.speaker }}</p>
              <p v-if="item.venue"><i class="fas fa-map-marker-alt"></i> {{ item.venue }}</p>
            </div>
          </div>
        </div>

        <!-- 无数据时显示默认内容 -->
        <div v-else class="day-section">
          <h3 class="day-title">
            <i class="fas fa-calendar-day"></i>
            会议议程
          </h3>
          <div class="list-item">
            <p>暂无议程数据，请稍后刷新...</p>
          </div>
        </div>

        <!-- 下载议程 -->
        <div class="download-section">
          <button class="submit-btn" @click="downloadAgenda">
            <i class="fas fa-download"></i>
            下载完整议程
          </button>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { getList } from '@/api/agenda/agenda';
import apiMixin from '@/mixins/apiMixin';
import { dataTransformers } from '@/utils/apiHelper';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import ApiStatus from '@/components/ApiStatus.vue';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'Agenda',
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
    ApiStatus
  },
  data() {
    return {
      mainTitle: '',
      subTitle:'',
      agendaList: [],
      dataSource: 'unknown',
      responseTime: 0,
      // 默认议程数据
      defaultAgendaData: [
        { id: 1, time: '08:30-09:00', topic: '签到注册', speaker: '会务组', venue: '主会场大厅', description: '参会者签到，领取会议资料', date: '2025-09-15' },
        { id: 2, time: '09:00-09:30', topic: '开幕式', speaker: '主办方领导', venue: '主会场', description: '大会开幕致辞，嘉宾介绍', date: '2025-09-15' },
        { id: 3, time: '09:30-10:30', topic: '主题演讲：数智攀登，管理跃升', speaker: '公司领导', venue: '主会场', description: '探讨数字化智能化在企业管理中的创新应用', date: '2025-09-15' },
        { id: 4, time: '10:30-10:45', topic: '茶歇', speaker: '', venue: '休息区', description: '休息时间，自由交流', date: '2025-09-15' },
        { id: 5, time: '10:45-12:00', topic: '圆桌讨论：企业管理创新实践', speaker: '各部门负责人', venue: '主会场', description: '管理层共同探讨企业管理现代化转型路径', date: '2025-09-15' },
        { id: 6, time: '12:00-13:30', topic: '午餐时间', speaker: '', venue: '餐厅', description: '自助午餐，网络交流', date: '2025-09-15' }
      ]
    }
  },
  computed: {
    /**
     * 按日期分组的议程数据，并按时间顺序排序
     */
    groupedAgenda() {
      const grouped = {};

      // 按日期分组
      this.agendaList.forEach(item => {
        const date = item.date || '2025-09-15';
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(item);
      });

      // 按时间排序每一天的议程
      Object.keys(grouped).forEach(date => {
        grouped[date].sort((a, b) => {
          // 提取开始时间进行比较
          const timeA = this.extractStartTime(a.time);
          const timeB = this.extractStartTime(b.time);
          return timeA.localeCompare(timeB);
        });
      });

      // 将分组对象转换为按日期排序的数组
      const sortedDates = Object.keys(grouped).sort((a, b) => {
        // 按日期排序，确保第一天在前
        return new Date(a).getTime() - new Date(b).getTime();
      });

      // 返回按日期排序的对象
      const sortedGrouped = {};
      sortedDates.forEach(date => {
        sortedGrouped[date] = grouped[date];
      });

      return sortedGrouped;
    }
  },
  async mounted() {
    await this.loadAgendaData();
    await this.loadData();
  },
  methods: {
async loadData() {
      const response = await getDictionary({
          code: 'hy_agenda' // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
          this.mainTitle = dictData.find(item => item.dictValue === '主标题').dictKey;
          this.subTitle= dictData.find(item => item.dictValue === '副标题').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },


    /**
     * 加载议程数据
     */
    async loadAgendaData() {
      const startTime = Date.now();

      try {
        console.log('开始加载议程数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.agenda(response.data);
          console.log('转换后的数据:', transformedData);

          this.agendaList = transformedData;
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载议程数据失败:', error);
        this.agendaList = this.defaultAgendaData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新议程数据
     */
    async refreshData() {
      await this.loadAgendaData();
    },

    /**
     * 格式化议程数据
     */
    formatApiData(data, type) {
      if (type === 'array' && Array.isArray(data)) {
        return dataTransformers.agenda(data);
      }
      return data;
    },

    /**
     * 下载议程文件
     */
    downloadAgenda(event) {
      const btn = event.target;
      const originalText = btn.innerHTML;

      btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备下载...';
      btn.disabled = true;

      setTimeout(() => {
        // 这里可以实现真实的下载逻辑
        if (this.$message) {
          this.$message.success('议程文件下载完成！');
        } else {
          alert('议程文件已准备好！\n（这是一个演示功能）');
        }
        btn.innerHTML = originalText;
        btn.disabled = false;
      }, 2000);
    },

    /**
     * 处理API测试成功
     */
    handleTestSuccess(result) {
      console.log('API测试成功:', result);
      this.responseTime = result.responseTime;
    },

    /**
     * 处理API测试错误
     */
    handleTestError(result) {
      console.error('API测试失败:', result);
      this.hasError = true;
      this.errorMessage = result.error || 'API测试失败';
    },

    /**
     * 获取API测试调用函数
     * 返回一个绑定了正确上下文的函数用于API状态监控组件测试
     */
    getTestApiCall() {
      console.log('获取API测试调用函数...');
      return async () => {
        console.log('执行API测试调用...');
        try {
          const response = await getList(1, 10, {});
          console.log('API测试响应:', response);
          return response;
        } catch (error) {
          console.error('API测试调用失败:', error);
          throw error;
        }
      };
    },

    /**
     * 提取开始时间用于排序
     */
    extractStartTime(timeStr) {
      if (!timeStr) return '00:00:00';

      // 处理 "08:30:00-09:00:00" 或 "08:30-09:00" 格式
      const startTime = timeStr.split('-')[0].trim();

      // 确保时间格式为 HH:MM:SS
      if (startTime.length === 5) {
        return startTime + ':00'; // 08:30 -> 08:30:00
      }

      return startTime || '00:00:00';
    },

    /**
     * 格式化日期显示
     */
    formatDate(dateStr) {
      if (!dateStr) return '会议议程';

      try {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 获取所有日期并排序，动态确定是第几天
        const allDates = [...new Set(this.agendaList.map(item => item.date))].sort();
        const dayIndex = allDates.indexOf(dateStr);

        if (dayIndex >= 0) {
          const dayNumber = dayIndex + 1;
          return `第${dayNumber}天 - ${year}年${month}月${day}日`;
        } else {
          return `${year}年${month}月${day}日`;
        }
      } catch (error) {
        return dateStr;
      }
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

/* 页面内容 */
.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

/* 表单容器 */
.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

/* 列表页面样式 */
.list-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #4682B4;
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease forwards;
}

.list-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.list-item h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 8px;
}

.list-item p {
    color: #666;
    font-size: 14px;
    margin: 5px 0;
}

.list-item .time {
    background: #f0f8ff;
    color: #4682B4;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 10px;
}

.day-section {
    margin-bottom: 30px;
}

.day-title {
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    color: white;
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.download-section {
    margin-top: 30px;
    text-align: center;
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.list-item:nth-child(1) { animation-delay: 0.1s; }
.list-item:nth-child(2) { animation-delay: 0.2s; }
.list-item:nth-child(3) { animation-delay: 0.3s; }
.list-item:nth-child(4) { animation-delay: 0.4s; }
.list-item:nth-child(5) { animation-delay: 0.5s; }
</style>