<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-robot"></i>
          <h2>会务助手</h2>
          <p>AI智能问答，为您解答会务相关问题</p>
        </div>

        <!-- 快捷问题 -->
        <div class="quick-questions">
          <h3>常见问题</h3>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <span>正在加载常见问题...</span>
          </div>

          <!-- 错误提示 -->
          <div v-else-if="hasError" class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <span>{{ errorMessage }}</span>
            <button class="retry-btn" @click="loadQuickQuestions">重试</button>
          </div>

          <!-- 问题列表 -->
          <div v-else class="question-grid">
            <button v-for="question in quickQuestions" :key="question" class="question-btn" @click="askQuestion(question)">
              {{ question }}
            </button>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area" id="chatArea" ref="chatArea">
          <div v-for="message in messages" :key="message.id" class="chat-message" :class="message.type">
            <div v-if="message.type === 'system'" class="system-message">
              <i class="fas fa-info-circle"></i>
              <span>{{ message.content }}</span>
            </div>
            <div v-else-if="message.type === 'user'" class="user-message">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-avatar">
                <i class="fas fa-user"></i>
              </div>
            </div>
            <div v-else class="ai-message">
              <div class="message-avatar">
                <i class="fas fa-robot"></i>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>

          <!-- 打字指示器 -->
          <div v-if="isTyping" class="typing-indicator">
            <div class="message-avatar">
              <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <input
              v-model="currentMessage"
              type="text"
              placeholder="请输入您的问题..."
              @keypress="handleKeyPress"
              :disabled="isTyping"
            >
            <button @click="sendMessage" :disabled="isTyping || !currentMessage.trim()">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { getDictionary } from '@/api/system/dictbiz';

export default {
  name: 'AiChat',
  data() {
    return {
      currentMessage: '',
      isTyping: false,
      messages: [
        { id: 1, type: 'system', content: '欢迎使用会务助手！我可以为您解答会议相关问题。' }
      ],
      quickQuestions: [],
      isLoading: false,
      hasError: false,
      errorMessage: '',
      // 默认快捷问题（作为兜底数据）
      defaultQuickQuestions: [
        '会议时间安排',
        '会议地点在哪',
        '如何签到',
        '用餐安排',
        '住宿信息',
        '联系方式'
      ],
      responses: {
        '会议时间安排': '会议时间为2025年9月15日-16日，共两天。第一天08:30开始签到，09:00正式开始。详细议程请查看"议程"页面。',
        '会议地点在哪': '会议地点在广东烟草大厦会议中心，地址：广州市天河区珠江新城。可乘坐地铁3号线/5号线到珠江新城站。',
        '如何签到': '您可以通过扫码签到或手动签到两种方式。建议提前15分钟到达会场，在签到页面完成签到流程。',
        '用餐安排': '会议期间提供工作餐，午餐时间为12:00-13:30。如有特殊饮食要求，请提前联系会务组。',
        '住宿信息': '推荐住宿：广州大酒店（步行3分钟）、珠江宾馆（步行8分钟）。详细信息请查看"参会指南"页面。',
        '联系方式': '会务组联系人：张先生 138-0000-0000，技术支持：李女士 139-0000-0000。如有紧急情况请及时联系。'
      }
    }
  },
  async mounted() {
    await this.loadQuickQuestions();
  },
  methods: {
    /**
     * 加载快捷问题数据
     */
    async loadQuickQuestions() {
      this.isLoading = true;
      this.hasError = false;
      this.errorMessage = '';

      try {
        console.log('开始加载快捷问题数据...');

        // 调用字典API获取快捷问题
        const response = await getDictionary({
          code: 'hy_aichat_question' // 字典编码，需要在后台配置
        });

        console.log('快捷问题API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;

          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取问题文本
            this.quickQuestions = dictData.map(item => item.dictValue || item.label || item.text);
            console.log('从API加载的快捷问题:', this.quickQuestions);
          } else {
            console.log('API返回数据为空，使用默认快捷问题');
            this.quickQuestions = [...this.defaultQuickQuestions];
          }
        } else {
          throw new Error('API响应格式不正确');
        }

      } catch (error) {
        console.error('加载快捷问题失败:', error);
        this.hasError = true;
        this.errorMessage = `加载快捷问题失败: ${error.message}`;

        // 使用默认数据作为兜底
        this.quickQuestions = [...this.defaultQuickQuestions];
        console.log('使用默认快捷问题作为兜底');
      } finally {
        this.isLoading = false;
      }
    },

    askQuestion(question) {
      this.sendMessage(question);
    },
    handleKeyPress(event) {
      if (event.key === 'Enter') {
        this.sendMessage();
      }
    },
    sendMessage(predefinedMessage = null) {
      const message = predefinedMessage || this.currentMessage.trim();
      if (!message) return;

      // 添加用户消息
      this.addMessage(message, 'user');

      // 清空输入框
      if (!predefinedMessage) {
        this.currentMessage = '';
      }

      // 显示打字指示器
      this.showTypingIndicator();

      // 模拟AI回复
      setTimeout(() => {
        this.hideTypingIndicator();
        const response = this.getAIResponse(message);
        this.addMessage(response, 'ai');
      }, 1500);
    },
    addMessage(content, type) {
      this.messages.push({
        id: Date.now(),
        type: type,
        content: content
      });

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    showTypingIndicator() {
      this.isTyping = true;
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    hideTypingIndicator() {
      this.isTyping = false;
    },
    scrollToBottom() {
      const chatArea = this.$refs.chatArea;
      if (chatArea) {
        chatArea.scrollTop = chatArea.scrollHeight;
      }
    },
    getAIResponse(message) {
      // 检查是否有预设回答
      for (const [key, value] of Object.entries(this.responses)) {
        if (message.includes(key) || message.includes(key.replace(/\s/g, ''))) {
          return value;
        }
      }

      // 关键词匹配
      if (message.includes('时间') || message.includes('几点') || message.includes('什么时候')) {
        return this.responses['会议时间安排'];
      }

      if (message.includes('地点') || message.includes('地址') || message.includes('在哪')) {
        return this.responses['会议地点在哪'];
      }

      if (message.includes('签到') || message.includes('报到')) {
        return this.responses['如何签到'];
      }

      if (message.includes('吃饭') || message.includes('用餐') || message.includes('午餐')) {
        return this.responses['用餐安排'];
      }

      if (message.includes('住宿') || message.includes('酒店') || message.includes('宾馆')) {
        return this.responses['住宿信息'];
      }

      if (message.includes('联系') || message.includes('电话') || message.includes('咨询')) {
        return this.responses['联系方式'];
      }

      // 默认回复
      return '抱歉，我暂时无法理解您的问题。您可以尝试点击上方的常见问题，或者换个方式提问。如需人工服务，请联系会务组：138-0000-0000。';
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
}

.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
}

.form-header {
    text-align: center;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

.quick-questions {
    margin-bottom: 5px;
}

.quick-questions h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 5px;
    text-align: center;
}

.question-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.question-btn {
    background: rgba(70, 130, 180, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.3);
    color: #4682B4;
    padding: 10px;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.question-btn:hover {
    background: #4682B4;
    color: white;
    transform: translateY(-2px);
}

/* 加载状态样式 */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    color: #4682B4;
    font-size: 14px;
}

.loading-state i {
    font-size: 16px;
}

/* 错误状态样式 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    color: #e74c3c;
    font-size: 14px;
    text-align: center;
}

.error-state i {
    font-size: 20px;
    margin-bottom: 5px;
}

.retry-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.chat-area {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 15px;
    max-height: 300px;
}

.chat-message {
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease;
}

.system-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    padding: 10px;
    border-radius: 8px;
    font-size: 13px;
    text-align: center;
}

.user-message {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    gap: 10px;
}

.ai-message {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    gap: 10px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: #4682B4;
    color: white;
}

.ai-message .message-avatar {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
}

.message-content {
    max-width: 70%;
    padding: 12px 15px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message .message-content {
    background: #4682B4;
    color: white;
    border-bottom-right-radius: 5px;
}

.ai-message .message-content {
    background: white;
    color: #333;
    border: 1px solid #e1e5e9;
    border-bottom-left-radius: 5px;
}

.typing-indicator {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    gap: 10px;
    margin-bottom: 15px;
}

.typing-dots {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 18px;
    border-bottom-left-radius: 5px;
    padding: 12px 15px;
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #4682B4;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.input-area {
    margin-top: auto;
}

.input-container {
    display: flex;
    gap: 10px;
    background: white;
    border-radius: 25px;
    padding: 5px;
    border: 1px solid #e1e5e9;
}

.input-container input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 15px;
    font-size: 14px;
    background: transparent;
}

.input-container button {
    background: #4682B4;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-container button:hover:not(:disabled) {
    background: #1E90FF;
    transform: scale(1.1);
}

.input-container button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>