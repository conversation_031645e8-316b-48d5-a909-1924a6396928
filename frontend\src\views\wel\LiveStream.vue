<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-broadcast-tower"></i>
          <h2>{{ mainTitle }}</h2>
          <p>{{subTitle}}</p>
        </div>

        <!-- 加载状态 -->
        <LoadingIndicator
          :show="isLoading"
          text="正在加载直播信息..."
          size="small"
        />

        <!-- 错误提示 -->
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          :message="errorMessage"
          :show-retry="true"
          :retrying="isLoading"
          @retry="refreshData"
          @close="clearError"
        />

        <!-- 直播状态 -->
        <div class="live-status">
          <div class="status-card" id="liveStatus">
            <div class="status-indicator">
              <div class="live-dot" :class="{ offline: !isLive }"></div>
              <span id="statusText">{{ statusText }}</span>
            </div>
            <div class="viewer-count">
              <i class="fas fa-eye"></i>
              <span id="viewerCount">{{ viewerCount }}</span> 人观看
            </div>
          </div>
        </div>

        <!-- 直播窗口 -->
        <div class="live-player">
          <div class="player-container" id="playerContainer">
            <div class="player-placeholder" v-if="!isLive">
              <i class="fas fa-play-circle"></i>
              <h3>直播即将开始</h3>
              <p>请稍候，直播将在会议开始时自动播放</p>
            </div>
            <iframe v-else class="live-iframe" :src="liveUrl" frameborder="0" allowfullscreen></iframe>
          </div>
          <div class="player-controls">
            <button class="control-btn" id="playBtn" @click="togglePlay">
              <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
            <button class="control-btn" @click="toggleMute">
              <i :class="isMuted ? 'fas fa-volume-mute' : 'fas fa-volume-up'" id="volumeIcon"></i>
            </button>
            <button class="control-btn" @click="toggleFullscreen">
              <i class="fas fa-expand"></i>
            </button>
            <button class="control-btn" @click="shareStream">
              <i class="fas fa-share"></i>
            </button>
          </div>
        </div>

        <!-- 直播信息 -->
        <div class="live-info">
          <h3>直播信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <i class="fas fa-calendar"></i>
              <div>
                <strong>直播时间</strong>
                <p>{{ formattedLiveTime }}</p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-microphone"></i>
              <div>
                <strong>当前议程</strong>
                <p id="currentAgenda">{{ currentAgenda }}</p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-user"></i>
              <div>
                <strong>主讲人</strong>
                <p id="currentSpeaker">{{ currentSpeaker }}</p>
              </div>
            </div>

            <div class="info-item">
              <i class="fas fa-signal"></i>
              <div>
                <strong>直播质量</strong>
                <p>
                  <select id="qualitySelect" @change="changeQuality" v-model="selectedQuality">
                    <option value="auto">自动</option>
                    <option value="1080p">1080P 高清</option>
                    <option value="720p">720P 标清</option>
                    <option value="480p">480P 流畅</option>
                  </select>
                </p>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </main>
  </div>
</template>
<script>
import { getList } from '@/api/livestream/liveStream';
import { dataTransformers } from '@/utils/apiHelper';
import apiMixin from '@/mixins/apiMixin';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'LiveStream',
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage
  },
  data() {
    return {
      mainTitle:'',
      subTitle:'',
      liveUrl: 'https://player.bilibili.com/player.html?aid=123456',
      isLive: false,
      isPlaying: false,
      isMuted: false,
      hasLiked: false,
      likeCount: 0,
      commentCount: 0,
      viewerCount: 0,
      statusText: '即将开始',
      currentAgenda: '开幕式致辞',
      currentSpeaker: '公司领导',
      selectedQuality: 'auto',
      newMessage: '',
      chatMessages: [],
      dataSource: 'unknown',
      responseTime: 0,
      // 直播时间相关
      startTime: '',
      endTime: '',
      // 默认直播数据
      defaultLiveData: [
        {
          id: 1,
          url: 'https://player.bilibili.com/player.html?aid=123456',
          title: '企业管理现场会直播',
          status: 'preparing',
          statusText: '即将开始2',
          viewerCount: 0,
          startTime: '2025-09-15 09:00:00',
          endTime: '2025-09-15 17:00:00',
          speaker: '公司领导'
        }
      ]
    }
  },
  computed: {
    /**
     * 格式化直播时间显示
     * 将开始时间和结束时间合并显示，如：15号5:00-6:00
     */
    formattedLiveTime() {
      if (!this.startTime || !this.endTime) {
        return '2025年9月15日 09:00 - 17:00'; // 默认时间
      }

      try {
        const startDate = new Date(this.startTime);
        const endDate = new Date(this.endTime);

        // 检查日期是否有效
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return '2025年9月15日 09:00 - 17:00'; // 默认时间
        }
        // 格式化开始时间
        const startDay = startDate.getMonth()+'月'+endDate.getDate();
        const startHour = startDate.getHours().toString().padStart(2, '0');
        const startMinute = startDate.getMinutes().toString().padStart(2, '0');

        // 格式化结束时间
        const endDay = startDate.getMonth()+'月'+endDate.getDate();
        const endHour = endDate.getHours().toString().padStart(2, '0');
        const endMinute = endDate.getMinutes().toString().padStart(2, '0');

        // 如果是同一天，显示格式：15号5:00-6:00
        if (startDay === endDay) {
          return `${startDay}号${startHour}:${startMinute}-${endHour}:${endMinute}`;
        } else {
          // 如果不是同一天，显示完整格式
          return `${startDay}号${startHour}:${startMinute}-${endDay}号${endHour}:${endMinute}`;
        }
      } catch (error) {
        console.error('时间格式化错误:', error);
        return '2025年9月15日 09:00 - 17:00'; // 默认时间
      }
    }
  },
  async mounted() {
    await this.loadLiveStreamData();
    await this.loadData();
    // 初始化
    this.updateViewerCount();
    setInterval(this.updateViewerCount, 30000); // 每30秒更新一次观看人数

    // 模拟自动消息
    setTimeout(() => {
      this.chatMessages.push({
        user: '张经理',
        text: '演讲内容很精彩！'
      });
      this.commentCount++;
    }, 5000);
  },
  methods: {
    async loadData() {
      const response = await getDictionary({
          code: 'hy_live_stream' // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
          this.mainTitle = dictData.find(item => item.dictValue === '主标题').dictKey;
          this.subTitle= dictData.find(item => item.dictValue === '副标题').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },
    /**
     * 加载直播数据
     */
    async loadLiveStreamData() {
      const startTime = Date.now();

      try {
        console.log('开始加载直播数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('直播API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.liveStream(response.data);
          console.log('转换后的直播数据:', transformedData);

          if (transformedData && transformedData.length > 0) {
            console.log("开始赋值")
            const liveInfo = transformedData[0];
            this.liveUrl = liveInfo.url || this.liveUrl;
            this.statusText = liveInfo.statusText || (liveInfo.status === 'live' ? '直播中' : '即将开始');
            this.isLive = liveInfo.status === 'live';
            this.viewerCount = liveInfo.viewerCount || 0;
            this.currentAgenda = liveInfo.title || this.currentAgenda;
            this.currentSpeaker = liveInfo.speaker || this.currentSpeaker;
            // 读取开始和结束时间
            this.startTime = liveInfo.startTime || '';
            this.endTime = liveInfo.endTime || '';
            console.log(liveInfo)
            }

          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载直播数据失败:', error);

        // 使用默认数据
        if (this.defaultLiveData && this.defaultLiveData.length > 0) {
          const liveInfo = this.defaultLiveData[0];
          this.liveUrl = liveInfo.url || this.liveUrl;
          this.statusText = liveInfo.status === 'live' ? '直播中' : '即将开始';
          this.isLive = liveInfo.status === 'live';
          this.viewerCount = liveInfo.viewerCount || 0;
          this.currentAgenda = liveInfo.title || this.currentAgenda;
          this.currentSpeaker = liveInfo.speaker || this.currentSpeaker;
          // 读取默认的开始和结束时间
          this.startTime = liveInfo.startTime || '';
          this.endTime = liveInfo.endTime || '';
        }

        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新直播数据
     */
    async refreshData() {
      await this.loadLiveStreamData();
    },

    /**
     * 格式化直播数据
     */
    formatApiData(data, type) {
      if (type === 'array' && Array.isArray(data)) {
        return dataTransformers.liveStream(data);
      }
      return data;
    },
    togglePlay() {
      if (this.isPlaying) {
        this.statusText = '已暂停';
        this.isPlaying = false;
      } else {
        this.statusText = '直播中';
        this.isPlaying = true;
        this.isLive = true;
      }
    },
    toggleMute() {
      this.isMuted = !this.isMuted;
    },
    toggleFullscreen() {
      alert('全屏功能\n（演示功能）\n在实际应用中会切换到全屏模式');
    },
    shareStream() {
      if (navigator.share) {
        navigator.share({
          title: '企业管理现场会直播',
          text: '正在观看企业管理现场会直播',
          url: window.location.href
        });
      } else {
        alert('分享直播\n（演示功能）\n在实际应用中会调用分享功能');
      }
    },
    changeQuality() {
      alert(`切换到${this.selectedQuality}画质\n（演示功能）`);
    },
    sendMessage() {
      const message = this.newMessage.trim();

      if (message) {
        this.chatMessages.push({
          user: '我',
          text: message
        });

        this.newMessage = '';
        this.commentCount++;

        // 滚动到底部
        this.$nextTick(() => {
          const chatMessages = this.$refs.chatMessages;
          if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
          }
        });
      }
    },
    handleKeyPress(event) {
      if (event.key === 'Enter') {
        this.sendMessage();
      }
    },
    likeStream() {
      if (!this.hasLiked) {
        this.hasLiked = true;
        this.likeCount++;
      }
    },
    updateViewerCount() {
      this.viewerCount = Math.floor(Math.random() * 50) + 100;
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

/* 页面内容 */
.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

/* 表单容器 */
.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

.live-status {
    margin: 20px 0;
}

.status-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.live-dot {
    width: 12px;
    height: 12px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.live-dot.offline {
    background: #ccc;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-indicator span {
    color: #333;
    font-weight: 500;
}

.viewer-count {
    color: #666;
    font-size: 14px;
}

.viewer-count i {
    color: #4682B4;
    margin-right: 5px;
}

.live-player {
    margin: 20px 0;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.player-container {
    width: 100%;
    height: 200px;
    background: #000;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-placeholder {
    text-align: center;
    color: white;
}

.player-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.7;
}

.player-placeholder h3 {
    margin-bottom: 10px;
    font-size: 18px;
}

.player-placeholder p {
    font-size: 14px;
    opacity: 0.8;
}

.live-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.player-controls {
    background: #f8f9fa;
    padding: 15px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.control-btn {
    background: none;
    border: none;
    color: #4682B4;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(70, 130, 180, 0.1);
}

.live-info {
    margin: 30px 0;
}

.live-info h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 15px;
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-item i {
    color: #4682B4;
    font-size: 18px;
    width: 20px;
}

.info-item strong {
    color: #333;
    font-size: 14px;
    display: block;
    margin-bottom: 3px;
}

.info-item p {
    color: #666;
    font-size: 13px;
    margin: 0;
}

#qualitySelect {
    background: rgba(70, 130, 180, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.3);
    border-radius: 5px;
    padding: 3px 8px;
    font-size: 12px;
    color: #4682B4;
}

.interaction-area {
    margin: 30px 0;
}

.interaction-area h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 15px;
    text-align: center;
}

.chat-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
}

.chat-messages {
    height: 150px;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
}

.chat-message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 13px;
}

.chat-message.system {
    background: rgba(70, 130, 180, 0.1);
    color: #4682B4;
    text-align: center;
}

.chat-message.user {
    background: white;
    border-left: 3px solid #4682B4;
}

.chat-input {
    display: flex;
    padding: 15px;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.chat-input button {
    background: #4682B4;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-input button:hover {
    background: #1E90FF;
}

.interaction-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.like-btn {
    background: none;
    border: none;
    color: #4682B4;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.like-btn:hover {
    color: #ff4757;
}

.like-btn.liked {
    color: #ff4757;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    font-size: 14px;
}

.stat-item i {
    color: #4682B4;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .live-container {
    padding: 10px;
  }

  .video-player {
    height: 200px;
  }

  .controls {
    flex-wrap: wrap;
    gap: 8px;
  }

  .control-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .chat-container {
    height: 250px;
  }

  .chat-input {
    flex-direction: column;
    gap: 10px;
  }

  .chat-input input {
    margin-right: 0;
  }
}
</style>