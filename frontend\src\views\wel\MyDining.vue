<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-utensils"></i>
          <h2>用餐安排</h2>
          <p>会议期间的餐饮服务信息</p>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在加载用餐信息...</p>
          </div>
        </div>

        <!-- 用餐状态卡片 -->
        <div v-if="!loading" class="dining-status">
          <div class="status-card">
            <div class="status-header">
              <h3>今日用餐状态</h3>
              <span class="date">{{ currentDate }}</span>
            </div>
            <div class="meal-progress">
              <div v-for="meal in mealStatus" :key="meal.type" class="meal-item">
                <div class="meal-info">
                  <i :class="meal.icon"></i>
                  <span>{{ meal.name }}</span>
                </div>
                <div :class="['meal-status', meal.status]">
                  <i :class="meal.statusIcon"></i>
                  {{ meal.statusText }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 餐券信息 -->
        <div v-if="!loading" class="meal-tickets">
          <h3>我的餐券</h3>
          <div class="ticket-list">
            <div v-for="ticket in tickets" :key="ticket.id" :class="['ticket-item', ticket.status]">
              <div class="ticket-header">
                <div class="ticket-type">
                  <i :class="ticket.icon"></i>
                  <span>{{ ticket.type }}</span>
                </div>
                <div class="ticket-status">{{ ticket.statusText }}</div>
              </div>
              <div class="ticket-details">
                <p><strong>时间：</strong>{{ ticket.time }}</p>
                <p><strong>地点：</strong>{{ ticket.location }}</p>
                <p v-if="ticket.menu"><strong>菜单：</strong>{{ ticket.menu }}</p>
                <p v-if="ticket.usedTime"><strong>使用时间：</strong>{{ ticket.usedTime }}</p>
              </div>
              <button
                v-if="ticket.status === 'available'"
                class="use-ticket-btn"
                @click="useTicket(ticket.type)"
              >
                <i class="fas fa-qrcode"></i>
                使用餐券
              </button>
            </div>
          </div>
        </div>

        <!-- 菜单预览 -->
        <div v-if="!loading" class="menu-preview">
          <h3>今日菜单</h3>
          <div class="menu-tabs">
            <button
              v-for="menu in menus"
              :key="menu.type"
              :class="['menu-tab', { active: activeMenu === menu.type }]"
              @click="switchMenu(menu.type)"
            >
              {{ menu.name }}
            </button>
          </div>

          <div v-for="menu in menus" :key="menu.type" class="menu-content" :style="{ display: activeMenu === menu.type ? 'block' : 'none' }">
            <div class="menu-section">
              <h4>{{ menuData[menu.type].title }}</h4>
              <div class="dish-list">
                <div v-for="dish in menuData[menu.type].dishes" :key="dish.name" class="dish-item">
                  <span>{{ dish.name }}</span>
                  <small>{{ dish.description }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        
      </div>
    </main>
  </div>
</template>

<script>
import { getCurrentUserSchedule } from '@/api/schedule/userSchedule';

export default {
  name: 'MyDining',
  data() {
    return {
      loading: true,
      currentDate: new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      activeMenu: 'lunch',
      diningInfo: null,
      // 用餐状态
      mealStatus: [
        {
          type: 'breakfast',
          name: '早餐',
          icon: 'fas fa-coffee',
          status: 'completed',
          statusIcon: 'fas fa-check',
          statusText: '已用餐'
        },
        {
          type: 'lunch',
          name: '午餐',
          icon: 'fas fa-utensils',
          status: 'pending',
          statusIcon: 'fas fa-clock',
          statusText: '待用餐'
        },
        {
          type: 'dinner',
          name: '晚餐',
          icon: 'fas fa-moon',
          status: 'pending',
          statusIcon: 'fas fa-clock',
          statusText: '待用餐'
        }
      ],
      // 餐券信息
      tickets: [
        {
          id: 1,
          type: '早餐券',
          icon: 'fas fa-coffee',
          status: 'used',
          statusText: '已使用',
          time: '07:30 - 08:30',
          location: '大厦1楼餐厅',
          usedTime: '08:15'
        },
        {
          id: 2,
          type: '午餐券',
          icon: 'fas fa-utensils',
          status: 'available',
          statusText: '可使用',
          time: '12:00 - 13:30',
          location: '大厦1楼餐厅',
          menu: '商务套餐A'
        },
        {
          id: 3,
          type: '晚餐券',
          icon: 'fas fa-moon',
          status: 'available',
          statusText: '可使用',
          time: '18:00 - 19:30',
          location: '大厦2楼宴会厅',
          menu: '欢迎晚宴'
        }
      ],
      // 菜单信息
      menus: [
        { type: 'lunch', name: '午餐' },
        { type: 'dinner', name: '晚餐' }
      ],
      menuData: {
        lunch: {
          title: '商务套餐A',
          dishes: [
            { name: '白切鸡', description: '精选土鸡，口感鲜嫩' },
            { name: '清蒸鲈鱼', description: '新鲜鲈鱼，营养丰富' },
            { name: '时令蔬菜', description: '当季新鲜蔬菜' },
            { name: '白米饭', description: '优质大米' },
            { name: '例汤', description: '营养汤品' }
          ]
        },
        dinner: {
          title: '欢迎晚宴',
          dishes: [
            { name: '红烧狮子头', description: '传统名菜，肉质鲜美' },
            { name: '蒜蓉扇贝', description: '新鲜扇贝，蒜香浓郁' },
            { name: '宫保鸡丁', description: '经典川菜，香辣可口' },
            { name: '麻婆豆腐', description: '嫩滑豆腐，麻辣鲜香' },
            { name: '水果拼盘', description: '时令水果' }
          ]
        }
      },
      // 特殊饮食需求
      dietaryOptions: [
        { value: 'vegetarian', label: '素食' },
        { value: 'halal', label: '清真' },
        { value: 'no-spicy', label: '不吃辣' },
        { value: 'no-seafood', label: '不吃海鲜' },
        { value: 'diabetic', label: '糖尿病饮食' }
      ],
      selectedRequirements: [],
      requirementNote: ''
    };
  },
  methods: {
    /**
     * 切换菜单
     */
    switchMenu(menuType) {
      this.activeMenu = menuType;
    },

    /**
     * 使用餐券
     */
    useTicket(ticketType) {
      const ticket = this.tickets.find(t => t.type.includes(ticketType === 'lunch' ? '午餐' : '晚餐'));
      if (ticket) {
        if (this.$message) {
          this.$message.success(`${ticket.type}使用成功！`);
        } else {
          alert(`${ticket.type}使用成功！`);
        }
        // 更新餐券状态
        ticket.status = 'used';
        ticket.statusText = '已使用';
        ticket.usedTime = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

        // 更新用餐状态
        const mealType = ticketType === 'lunch' ? 'lunch' : 'dinner';
        const meal = this.mealStatus.find(m => m.type === mealType);
        if (meal) {
          meal.status = 'completed';
          meal.statusIcon = 'fas fa-check';
          meal.statusText = '已用餐';
        }
      }
    },

    /**
     * 提交特殊需求
     */
    submitRequirement() {
      if (this.selectedRequirements.length === 0 && !this.requirementNote.trim()) {
        if (this.$message) {
          this.$message.warning('请选择特殊需求或填写说明');
        } else {
          alert('请选择特殊需求或填写说明');
        }
        return;
      }

      const requirements = this.selectedRequirements.map(value =>
        this.dietaryOptions.find(option => option.value === value)?.label
      ).filter(Boolean);

      let message = '特殊饮食需求已提交：\n';
      if (requirements.length > 0) {
        message += `选择项目：${requirements.join('、')}\n`;
      }
      if (this.requirementNote.trim()) {
        message += `其他说明：${this.requirementNote.trim()}`;
      }

      if (this.$message) {
        this.$message.success('特殊饮食需求提交成功！');
      } else {
        alert(message);
      }

      // 清空表单
      this.selectedRequirements = [];
      this.requirementNote = '';
    },

    // 加载用户用餐信息
    async loadUserDining() {
      try {
        this.loading = true;
        const response = await getCurrentUserSchedule();

        if (response.data.success && response.data.data && response.data.data.diningInfo) {
          // 解析用餐信息
          const diningData = typeof response.data.data.diningInfo === 'string'
            ? JSON.parse(response.data.data.diningInfo)
            : response.data.data.diningInfo;

          this.diningInfo = diningData;

          // 根据后台数据更新用餐状态和餐券信息
          this.updateMealStatusFromBackend(diningData);
          this.updateTicketsFromBackend(diningData);

        } else {
          console.log('未找到用餐信息，使用默认数据');
        }
      } catch (error) {
        console.error('加载用餐信息失败:', error);
        this.$message && this.$message.warning('加载用餐信息失败，显示默认内容');
      } finally {
        this.loading = false;
      }
    },

    // 根据后台数据更新用餐状态
    updateMealStatusFromBackend(diningData) {
      // 这里可以根据后台数据格式来更新用餐状态
      // 示例：如果后台有用餐状态信息
      if (diningData.mealStatus) {
        this.mealStatus = diningData.mealStatus;
      }
    },

    // 根据后台数据更新餐券信息
    updateTicketsFromBackend(diningData) {
      // 这里可以根据后台数据格式来更新餐券信息
      // 示例：如果后台有餐券信息
      if (diningData.tickets) {
        this.tickets = diningData.tickets;
      }
    }
  },

  mounted() {
    this.loadUserDining();
  }
};
</script>

<style scoped>
/* 页面容器 */
.page-container {
  max-width: 375px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.page-header h1 {
  color: white;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.header-placeholder {
  width: 40px;
}

/* 页面内容 */
.page-content {
  margin-top: 20px;
  min-height: calc(100vh - 80px);
}

/* 列表容器 */
.list-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: slideInUp 0.6s ease forwards;
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 30px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  margin: 20px 0;
}

.loading-spinner {
  text-align: center;
}

.loading-spinner i {
  font-size: 32px;
  color: #4682B4;
  margin-bottom: 15px;
}

.loading-spinner p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.form-header i {
  font-size: 48px;
  color: #4682B4;
  margin-bottom: 15px;
}

.form-header h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 10px;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

/* 用餐状态 */
.dining-status {
  margin: 30px 0;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-header h3 {
  color: #333;
  font-size: 16px;
  margin: 0;
}

.status-header .date {
  color: #4682B4;
  font-size: 12px;
  font-weight: 500;
}

.meal-progress {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.meal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 10px;
}

.meal-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meal-info i {
  color: #4682B4;
  font-size: 16px;
}

.meal-info span {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.meal-status {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.meal-status.completed {
  background: #d4edda;
  color: #155724;
}

.meal-status.pending {
  background: #fff3cd;
  color: #856404;
}

/* 餐券信息 */
.meal-tickets {
  margin: 30px 0;
}

.meal-tickets h3 {
  color: #333;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
}

.ticket-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ticket-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #4682B4;
}

.ticket-item.used {
  border-left-color: #6c757d;
  opacity: 0.7;
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.ticket-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-type i {
  color: #4682B4;
  font-size: 16px;
}

.ticket-type span {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.ticket-status {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 12px;
  background: #e9ecef;
  color: #495057;
}

.ticket-item.used .ticket-status {
  background: #f8d7da;
  color: #721c24;
}

.ticket-item.available .ticket-status {
  background: #d1ecf1;
  color: #0c5460;
}

.ticket-details {
  margin-bottom: 15px;
}

.ticket-details p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.use-ticket-btn {
  width: 100%;
  background: #4682B4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.use-ticket-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

/* 菜单预览 */
.menu-preview {
  margin: 30px 0;
}

.menu-preview h3 {
  color: #333;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
}

.menu-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 5px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.menu-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 10px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
}

.menu-tab.active {
  background: #4682B4;
  color: white;
}

.menu-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.menu-section h4 {
  color: #333;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #4682B4;
}

.dish-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dish-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dish-item span {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 3px;
}

.dish-item small {
  color: #666;
  font-size: 12px;
}

/* 特殊需求 */
.special-requirements {
  margin: 30px 0;
}

.special-requirements h3 {
  color: #333;
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
}

.requirement-form {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.requirement-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.requirement-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.requirement-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4682B4;
}

.requirement-option label {
  color: #333;
  font-size: 14px;
  cursor: pointer;
}

.requirement-note {
  margin-bottom: 20px;
}

.requirement-note label {
  display: block;
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.requirement-note textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  box-sizing: border-box;
}

.requirement-note textarea:focus {
  outline: none;
  border-color: #4682B4;
  box-shadow: 0 0 0 2px rgba(70, 130, 180, 0.2);
}

.submit-requirement-btn {
  width: 100%;
  background: #4682B4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-requirement-btn:hover {
  background: #3a6fa5;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 320px) {


  .list-container {
    padding: 15px;
  }

  .form-header i {
    font-size: 40px;
  }

  .form-header h2 {
    font-size: 20px;
  }

  .requirement-options {
    grid-template-columns: 1fr;
  }
}
</style>
