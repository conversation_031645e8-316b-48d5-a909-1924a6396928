<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-images"></i>
          <h2>{{ mainTitle }}</h2>
          <p>{{subTitle}}</p>
        </div>

        <!-- 相册分类 -->
        <div class="album-categories">
          <button class="category-btn" :class="{ active: activeCategory === 'all' }" @click="switchCategory('all')">全部</button>
          <button class="category-btn" :class="{ active: activeCategory === 'opening' }" @click="switchCategory('opening')">开幕式</button>
          <button class="category-btn" :class="{ active: activeCategory === 'speech' }" @click="switchCategory('speech')">主题演讲</button>
          <button class="category-btn" :class="{ active: activeCategory === 'discussion' }" @click="switchCategory('discussion')">讨论交流</button>
          <button class="category-btn" :class="{ active: activeCategory === 'group' }" @click="switchCategory('group')">合影留念</button>
        </div>


        <!-- 照片网格 -->
        <div class="photo-grid" id="photoGrid">
          <div v-for="photo in filteredPhotos" :key="photo.id" class="photo-item" @click="openLightbox(photo)">
            <img :src="photo.url" :alt="photo.title" class="photo-img">
            <div class="photo-overlay">
              <div class="photo-info">
                <h3>{{ photo.title }}</h3>
                <p>{{ photo.date }}</p>
              </div>
            </div>
          </div>
        </div>

       
      </div>
    </main>

    <!-- 灯箱模态框 -->
    <div v-if="lightboxPhoto" class="lightbox-overlay" @click="closeLightbox">
      <div class="lightbox-content" @click.stop>
        <button class="lightbox-close" @click="closeLightbox">
          <i class="fas fa-times"></i>
        </button>
        <img :src="lightboxPhoto.url" :alt="lightboxPhoto.title" class="lightbox-image">
        <div class="lightbox-info">
          <h3>{{ lightboxPhoto.title }}</h3>
          <p>{{ lightboxPhoto.description }}</p>
          <p class="photo-date">{{ lightboxPhoto.date }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getList } from '@/api/photoalbum/photoAlbum';
import apiMixin from '@/mixins/apiMixin';
import { dataTransformers } from '@/utils/apiHelper';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'Photo',
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage
  },
  data() {
    return {
      mainTitle:'',
      subTitle:'',
      photos: [],
      activeCategory: 'all',
      lightboxPhoto: null,
      dataSource: 'unknown',
      responseTime: 0,
      // 默认照片数据
      defaultPhotosData: [
        { id: 1, url: 'https://picsum.photos/300/200?random=1', title: '开幕式致辞', description: '公司领导发表开幕致辞', date: '2025-09-15 09:00', category: 'opening', likes: 15, liked: false },
        { id: 2, url: 'https://picsum.photos/300/200?random=2', title: '主题演讲', description: '数智攀登·管理跃升主题演讲', date: '2025-09-15 09:30', category: 'speech', likes: 23, liked: false },
        { id: 3, url: 'https://picsum.photos/300/200?random=3', title: '圆桌讨论', description: '企业管理创新实践讨论', date: '2025-09-15 10:45', category: 'discussion', likes: 18, liked: false },
        { id: 4, url: 'https://picsum.photos/300/200?random=4', title: '合影留念', description: '全体参会人员合影', date: '2025-09-15 12:00', category: 'group', likes: 42, liked: false },
        { id: 5, url: 'https://picsum.photos/300/200?random=5', title: '技术分享', description: '数字化管理技术分享', date: '2025-09-16 09:00', category: 'speech', likes: 31, liked: false },
        { id: 6, url: 'https://picsum.photos/300/200?random=6', title: '现场参观', description: '企业管理现代化成果展示', date: '2025-09-16 10:00', category: 'discussion', likes: 27, liked: false }
      ]
    }
  },
  computed: {
    filteredPhotos() {
      if (this.activeCategory === 'all') {
        return this.photos;
      }
      return this.photos.filter(photo => photo.category === this.activeCategory);
    },
    totalPhotos() {
      return this.filteredPhotos.length;
    },
    totalLikes() {
      return this.photos.reduce((sum, photo) => sum + photo.likes, 0);
    }
  },
  async mounted() {
    await this.loadPhotosData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      const response = await getDictionary({
          code: 'hy_photo' // 字典编码，需要在后台配置
        });
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
          this.mainTitle = dictData.find(item => item.dictValue === '主标题').dictKey;
          this.subTitle= dictData.find(item => item.dictValue === '副标题').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },
    /**
     * 加载照片数据
     */
    async loadPhotosData() {
      const startTime = Date.now();

      try {
        console.log('开始加载照片数据...');

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log('照片API响应:', response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.photos(response.data);
          console.log('转换后的照片数据:', transformedData);

          this.photos = transformedData;
          this.dataSource = 'api';
          this.hasError = false;
          this.errorMessage = '';
        } else {
          throw new Error('API响应格式不正确');
        }

        this.responseTime = Date.now() - startTime;

      } catch (error) {
        console.error('加载照片数据失败:', error);
        this.photos = this.defaultPhotosData;
        this.dataSource = 'fallback';
        this.hasError = true;
        this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新照片数据
     */
    async refreshData() {
      await this.loadPhotosData();
    },

    /**
     * 格式化照片数据
     */
    formatApiData(data, type) {
      if (type === 'array' && Array.isArray(data)) {
        return dataTransformers.photos(data);
      }
      return data;
    },
    switchCategory(category) {
      this.activeCategory = category;
    },
    uploadPhoto() {
      alert('上传照片功能\n（演示功能）\n在实际应用中会打开文件选择器');
    },
    openLightbox(photo) {
      this.lightboxPhoto = photo;
    },
    closeLightbox() {
      this.lightboxPhoto = null;
    },
    likePhoto(photo) {
      if (!photo.liked) {
        photo.liked = true;
        photo.likes++;
      }
    },
    downloadPhoto(photo) {
      alert(`下载照片：${photo.title}\n（演示功能）`);
    }
  }
}
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
}

.page-content {
    margin-top: 20px;
    min-height: calc(100vh - 80px);
}

.list-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: slideInUp 0.6s ease forwards;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header i {
    font-size: 48px;
    color: #4682B4;
    margin-bottom: 15px;
}

.form-header h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
    font-size: 14px;
}

.album-categories {
    display: flex;
    gap: 8px;
    margin: 20px 0;
    overflow-x: auto;
    padding: 5px 0;
}

.category-btn {
    background: rgba(70, 130, 180, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.3);
    color: #4682B4;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.category-btn.active,
.category-btn:hover {
    background: #4682B4;
    color: white;
}

.upload-section {
    text-align: center;
    margin: 20px 0;
}

.upload-btn {
    background: linear-gradient(135deg, #4682B4, #1E90FF);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.3);
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.photo-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.photo-img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 15px 10px 10px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.photo-item:hover .photo-overlay {
    transform: translateY(0);
}

.photo-info h3 {
    font-size: 12px;
    margin-bottom: 3px;
}

.photo-info p {
    font-size: 10px;
    opacity: 0.8;
    margin-bottom: 8px;
}

.photo-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 3px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.action-btn .fa-heart.liked {
    color: #ff4757;
}

.photo-stats {
    display: flex;
    justify-content: space-around;
    background: white;
    border-radius: 12px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.stat-item i {
    color: #4682B4;
    font-size: 16px;
}

.stat-item strong {
    color: #4682B4;
}

/* 灯箱样式 */
.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: white;
    border-radius: 12px;
    overflow: hidden;
}

.lightbox-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1001;
}

.lightbox-image {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

.lightbox-info {
    padding: 15px;
}

.lightbox-info h3 {
    color: #333;
    margin-bottom: 8px;
}

.lightbox-info p {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.photo-date {
    color: #999;
    font-size: 12px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>