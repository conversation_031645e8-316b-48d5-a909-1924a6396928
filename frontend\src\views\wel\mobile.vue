<template>
  <div class="mobile-container">
    <!-- 主标题区域 -->
    <section class="title-section" v-show="!currentComponent">
      <h1 class="main-title">{{ mainTitle || '数智攀登·管理跃升' }}</h1>
      <h2 class="conference-title">{{ subTitle || '企业管理现场会' }}</h2>
      <p class="organizer">{{ organizer || '主办单位：广东烟草广州市有限公司'}}</p>
      <p class="date">{{ date || '2025年9月'}}</p>
    </section>

    <!-- 页面头部 -->
    <header class="page-header" v-show="currentComponent">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h1>{{ getComponentTitle() }}</h1>
      <div class="header-placeholder"></div>
    </header>

    <!-- 功能模块区域 -->
    <section class="function-grid" v-show="!currentComponent">
      <div class="row-1">
        <div class="function-card" @click="showComponent('agenda')">
          <div class="card-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="card-text">
            <h3>会议议程</h3>
            <p>AGENDA</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('live-stream')">
          <div class="card-icon">
            <i class="fas fa-broadcast-tower"></i>
          </div>
          <div class="card-text">
            <h3>云直播</h3>
            <p>LIVE STREAM</p>
          </div>
        </div>
      </div>
      <div class="row-2">
        <div class="function-card" @click="showComponent('sub-venues')">
          <div class="card-icon">
            <i class="fas fa-video"></i>
          </div>
          <div class="card-text">
            <h3>分会场信息</h3>
            <p>SUB VENUES</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('materials')">
          <div class="card-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="card-text">
            <h3>会议资料</h3>
            <p>MATERIALS</p>
          </div>
        </div>
      </div>
      <div class="row-3">
        <div class="function-card" @click="showComponent('photo')">
          <div class="card-icon">
            <i class="fas fa-images"></i>
          </div>
          <div class="card-text">
            <h3>在线相册</h3>
            <p>PHOTO</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('guide')">
          <div class="card-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="card-text">
            <h3>参会指南</h3>
            <p>GUIDE</p>
          </div>
        </div>
      </div>
      <div class="row-4">
        <div class="function-card" @click="showComponent('ai-chat')">
          <div class="card-icon">
            <i class="fas fa-robot"></i>
          </div>
          <div class="card-text">
            <h3>会务助手</h3>
            <p>AI ASSISTANT</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('profile')">
          <div class="card-icon">
            <i class="fas fa-user"></i>
          </div>
          <div class="card-text">
            <h3>个人中心</h3>
            <p>PROFILE</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 动态组件显示区域 -->
    <div class="component-container" v-show="currentComponent">
      <component :is="currentComponentName" @navigate="handleNavigate" />
    </div>
  </div>
</template>

<script>
// 导入所有需要的组件
import Agenda from './Agenda.vue'
import LiveStream from './LiveStream.vue'
import SubVenues from './SubVenues.vue'
import Materials from './Materials.vue'
import Photo from './Photo.vue'
import Guide from './Guide.vue'
import AiChat from './AiChat.vue'
import Profile from './Profile.vue'
import MyDining from './MyDining.vue'
import MyAccommodation from './MyAccommodation.vue'
import MySchedule from './MySchedule.vue'
import { getDictionary } from '@/api/system/dictbiz'

export default {
  name: 'MobilePage',
  components: {
    Agenda,
    LiveStream,
    SubVenues,
    Materials,
    Photo,
    Guide,
    AiChat,
    Profile,
    MyDining,
    MyAccommodation,
    MySchedule
  },
  data() {
    return {
      currentComponent: null, // 当前显示的组件名称
      previousComponent: null, // 上一个组件，用于返回导航
      mainTitle: '', // 主标题，从字典获取
      subTitle:'',
      date:'',
      organizer:''
    }
  },
  async mounted() {
    await this.loadDictionary()
    this.loadFontAwesome()
  },
  computed: {
    currentComponentName() {
      if (!this.currentComponent) return null
      
      // 将组件名称映射到实际的组件名
      const componentMap = {
        'agenda': 'Agenda',
        'live-stream': 'LiveStream',
        'sub-venues': 'SubVenues',
        'materials': 'Materials',
        'photo': 'Photo',
        'guide': 'Guide',
        'ai-chat': 'AiChat',
        'profile': 'Profile',
        'my-dining': 'MyDining',
        'my-accommodation': 'MyAccommodation',
        'my-schedule': 'MySchedule'
      }
      
      return componentMap[this.currentComponent] || null
    }
  },
  methods: {
    loadFontAwesome() {
      // 动态引入Font Awesome（与首页保持一致）
      if (!document.getElementById('fa-mobile-page')) {
        const link = document.createElement('link');
        link.id = 'fa-mobile-page';
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(link);
      }
    },
    async loadDictionary() {
      try {
        const response = await getDictionary('wel_config')
        if (response && response.length > 0) {
          response.forEach(item => {
            if (item.dictKey === 'main_title') {
              this.mainTitle = item.dictValue
            } else if (item.dictKey === 'sub_title') {
              this.subTitle = item.dictValue
            } else if (item.dictKey === 'date') {
              this.date = item.dictValue
            } else if (item.dictKey === 'organizer') {
              this.organizer = item.dictValue
            }
          })
        }
      } catch (error) {
        console.error('加载字典数据失败:', error)
      }
    },
    showComponent(componentName) {
      this.currentComponent = componentName
    },
    goBack() {
      // 如果有上一个组件，返回到上一个组件，否则返回首页
      if (this.previousComponent) {
        this.currentComponent = this.previousComponent
        this.previousComponent = null
      } else {
        this.currentComponent = null
      }
    },
    handleNavigate(componentName) {
      this.previousComponent = this.currentComponent
      this.currentComponent = componentName
    },
    getComponentTitle() {
      const titleMap = {
        'agenda': '会议议程',
        'live-stream': '云直播',
        'sub-venues': '分会场信息',
        'materials': '会议资料',
        'photo': '在线相册',
        'guide': '参会指南',
        'ai-chat': '会务助手',
        'profile': '个人中心',
        'my-dining': '我的用餐',
        'my-accommodation': '我的住宿',
        'my-schedule': '我的日程'
      }
      return titleMap[this.currentComponent] || ''
    }
  }
}
</script>

<style scoped>
/* 移动端专用样式 - 基于首页样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 移动端容器 */
.mobile-container {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    min-height: 100vh;
    overflow-x: hidden;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%) !important;
    max-width: 100vw;
    margin: 0;
    padding: 20px;
    position: relative;
}

/* 标题区域 */
.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    padding-top: 30px;
}

.main-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.conference-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.organizer, .date {
    font-size: 14px;
    margin-bottom: 5px;
    opacity: 0.9;
}

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 0;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.page-header h1 {
    color: white;
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}

.header-placeholder {
    width: 40px;
}

/* 功能网格 */
.function-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.row-1, .row-2, .row-3, .row-4 {
    display: flex;
    gap: 15px;
}

.function-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    flex: 1;
}

.function-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.function-card:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.function-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.35);
}

.function-card:active {
    transform: translateY(-4px) scale(0.98);
    transition: all 0.1s ease;
}

.card-icon {
    margin-bottom: 8px;
}

.card-icon i {
    font-size: 24px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
}

/* FontAwesome 图标备用方案 */
.card-icon i.fas.fa-calendar-alt::before {
    content: "📅";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-broadcast-tower::before {
    content: "📡";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-video::before {
    content: "📹";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-file-alt::before {
    content: "📄";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-images::before {
    content: "🖼️";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-book-open::before {
    content: "📖";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-robot::before {
    content: "🤖";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-user::before {
    content: "👤";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-icon i.fas.fa-arrow-left::before {
    content: "←";
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.card-text {
    text-align: center;
}

.card-text h3 {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.card-text p {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* 组件容器 */
.component-container {
    width: 100%;
    min-height: calc(100vh - 80px);
}
</style>
