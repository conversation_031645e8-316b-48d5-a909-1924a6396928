# H5端功能模块设计

本文件详细说明demo首页8个功能模块的功能、涉及的数据表及主要参数。

---

## 1. 会议议程（AGENDA）
- 功能：展示会议的详细日程安排，包括时间、主题、演讲人、地点等。
- 主要表：agenda
- 字段示例：
  - id（主键）
  - date（日期）
  - start_time（开始时间）
  - end_time（结束时间）
  - topic（议题/主题）
  - speaker（演讲人）
  - venue（会场）
  - description（描述）

## 2. 云直播（LIVE STREAM）
- 功能：提供会议直播入口，展示直播链接、直播状态等。
- 主要表：live_stream
- 字段示例：
  - id（主键）
  - title（直播标题）
  - url（直播地址）
  - start_time（开始时间）
  - end_time（结束时间）
  - status（直播状态：未开始/进行中/已结束）
  - description（描述）

## 3. 分会场信息（SUB VENUES）
- 功能：展示各分会场的安排、议程、负责人等。
- 主要表：sub_venue
- 字段示例：
  - id（主键）
  - name（分会场名称）
  - location（地点）
  - agenda_id（关联议程）
  - manager（负责人）
  - description（描述）

## 4. 会议资料（MATERIALS）
- 功能：提供会议相关文件的下载和查阅。
- 主要表：materials
- 字段示例：
  - id（主键）
  - title（资料名称）
  - file_url（文件地址）
  - upload_time（上传时间）
  - uploader（上传人）
  - description（描述）

## 5. 在线相册（PHOTO）
- 功能：展示会议照片，支持分类浏览。
- 主要表：photo_album
- 字段示例：
  - id（主键）
  - title（相册名称）
  - image_url（图片地址）
  - upload_time（上传时间）
  - uploader（上传人）
  - category（分类）
  - description（描述）

## 6. 参会指南（GUIDE）
- 功能：为参会者提供交通、住宿、餐饮等实用信息。
- 主要表：guide
- 字段示例：
  - id（主键）
  - title（指南标题）
  - content（内容）
  - type（类型：交通/住宿/餐饮/其他）
  - update_time（更新时间）

## 7. 会务助手（AI ASSISTANT）
- 功能：AI智能问答，解答会务相关问题。
- 主要表：ai_chat_log
- 字段示例：
  - id（主键）
  - user_id（用户ID）
  - question（提问内容）
  - answer（AI回复）
  - create_time（提问时间）

## 8. 个人中心（PROFILE）
- 功能：展示和管理个人信息、参会记录等。
- 主要表：user_profile, attendance_record
- 字段示例：
  - user_profile：
    - id（主键）
    - name（姓名）
    - phone（手机号）
    - email（邮箱）
    - avatar（头像）
    - company（单位）
    - position（职位）
  - attendance_record：
    - id（主键）
    - user_id（用户ID）
    - agenda_id（议程ID）
    - checkin_time（签到时间）
    - status（签到状态）

---

如需扩展功能，可在此基础上增加表结构和字段。 